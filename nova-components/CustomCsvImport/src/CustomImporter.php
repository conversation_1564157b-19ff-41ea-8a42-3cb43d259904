<?php

namespace Capitalc\CustomCsvImport;

use Illuminate\Database\Eloquent\Model;
use SimonHamp\LaravelNovaCsvImport\Importer;
use Maatwebsite\Excel\Validators\Failure;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Custom CSV Importer that extends the base functionality
 * to support upsert operations (update existing records or create new ones)
 * while avoiding WithBatchInserts to prevent primary key constraint violations
 */
class CustomImporter extends Importer
{
    /**
     * Progress tracking callback
     */
    protected $progressCallback;

    /**
     * Current chunk processing stats
     */
    protected $processedRows = 0;
    protected $successfulRows = 0;
    protected $failedRows = 0;

    /**
     * Override batchSize to disable batch inserts and prevent primary key conflicts
     * By returning 1, we force individual processing of each model
     */
    public function batchSize(): int
    {
        return 1;
    }

    /**
     * Override chunkSize to process CSV data in smaller chunks of 5 rows
     * This improves performance and reduces memory usage for large CSV imports
     */
    public function chunkSize(): int
    {
        return 5;
    }

    /**
     * Set progress callback for chunk processing
     */
    public function setProgressCallback(callable $callback): self
    {
        $this->progressCallback = $callback;
        return $this;
    }

    /**
     * Get current processing stats
     */
    public function getProcessingStats(): array
    {
        return [
            'processed_rows' => $this->processedRows,
            'successful_rows' => $this->successfulRows,
            'failed_rows' => $this->failedRows,
        ];
    }

    /**
     * Reset processing stats
     */
    public function resetProcessingStats(): void
    {
        $this->processedRows = 0;
        $this->successfulRows = 0;
        $this->failedRows = 0;
    }
    /**
     * Override the model method to implement upsert functionality
     * and track progress for chunked processing
     *
     * @param array $row
     * @return Model
     */
    public function model(array $row): Model
    {
        $this->processedRows++;

        try {
            // If the row contains an 'id' field, attempt to find and update the existing model
            if (isset($row['id']) && !empty($row['id'])) {
                $model = $this->resource::newModel()->find($row['id']);

                if ($model) {
                    // Model exists, validate before updating
                    if ($this->validateUniqueConstraints($row, $model)) {
                        // Remove the ID from row data to prevent primary key conflicts
                        $updateData = $row;
                        unset($updateData['id']);

                        $model->fill($updateData);
                        $this->successfulRows++;
                        $this->callProgressCallback();
                        return $model;
                    } else {
                        // Validation failed, return empty model to skip this row
                        $this->failedRows++;
                        $this->callProgressCallback();
                        return $this->resource::newModel();
                    }
                } else {
                    // Model doesn't exist, mark this row as failed and skip it
                    $this->onFailure(new Failure(
                        $this->getCurrentRowNumber(),
                        'id',
                        ["Model with ID {$row['id']} not found in database"],
                        $row
                    ));

                    // Return a new empty model instance that will be skipped
                    $this->failedRows++;
                    $this->callProgressCallback();
                    return $this->resource::newModel();
                }
            } else {
                // No 'id' field present, create a new model
                // Remove any 'id' field that might be empty
                unset($row['id']);

                $model = $this->resource::newModel();

                // Validate unique constraints for new records too
                if ($this->validateUniqueConstraints($row, $model)) {
                    $model->fill($row);
                    $this->successfulRows++;
                    $this->callProgressCallback();
                    return $model;
                } else {
                    // Validation failed, return empty model to skip this row
                    $this->failedRows++;
                    $this->callProgressCallback();
                    return $this->resource::newModel();
                }
            }

        } catch (Exception $e) {
            $this->failedRows++;
            $this->callProgressCallback();

            // Log the error
            Log::warning('Row processing failed in CustomImporter', [
                'row' => $this->processedRows,
                'data' => $row,
                'error' => $e->getMessage()
            ]);

            // Return empty model to skip this row
            return $this->resource::newModel();
        }
    }

    /**
     * Call progress callback if set
     */
    private function callProgressCallback(): void
    {
        if ($this->progressCallback && is_callable($this->progressCallback)) {
            call_user_func($this->progressCallback, $this->getProcessingStats());
        }
    }

    /**
     * Override rules to make Title field optional for Product model during CSV import
     *
     * @return array
     */
    public function rules(): array
    {
        // Ensure rules is always an array, even if not set
        $rules = $this->rules ?? [];

        // Only modify title validation for Product model
        if ($this->isProductModel()) {
            // Make Title field not required if it exists in the rules
            if (isset($rules['title']) && is_array($rules['title'])) {
                $rules['title'] = array_filter($rules['title'], function($rule) {
                    return $rule !== 'required';
                });
            }

            // Also check for 'Title' with capital T
            if (isset($rules['Title']) && is_array($rules['Title'])) {
                $rules['Title'] = array_filter($rules['Title'], function($rule) {
                    return $rule !== 'required';
                });
            }
        }

        return $rules;
    }

    /**
     * Check if the current resource is a Product model
     *
     * @return bool
     */
    private function isProductModel(): bool
    {
        // Check if resource is set and is a Product resource
        if (isset($this->resource) && $this->resource instanceof \App\Nova\Product) {
            return true;
        }

        // Check if model_class is set and is a Product model
        if (isset($this->model_class) && $this->model_class === \App\Product::class) {
            return true;
        }

        // Check if model_class is set as string
        if (isset($this->model_class) && $this->model_class === 'App\Product') {
            return true;
        }

        return false;
    }

    /**
     * Validate unique constraints for the model being imported
     *
     * @param array $row
     * @param Model $model
     * @return bool
     */
    private function validateUniqueConstraints(array $row, Model $model): bool
    {
        // Only validate SKU uniqueness for Product models
        if ($this->isProductModel() && isset($row['sku']) && !empty($row['sku'])) {
            // Check if another product already has this SKU (excluding the current model if updating)
            $existingProduct = \App\Product::where('sku', $row['sku'])
                ->when($model->exists, function ($query) use ($model) {
                    return $query->where('id', '!=', $model->id);
                })
                ->first();

            if ($existingProduct) {
                $this->onFailure(new Failure(
                    $this->getCurrentRowNumber(),
                    'sku',
                    ["The SKU '{$row['sku']}' is already used by another product (ID: {$existingProduct->id}). SKUs must be unique."],
                    $row
                ));
                return false;
            }
        }

        return true;
    }

    /**
     * Get the current row number for failure reporting
     * Since we don't have direct access to the row number in the model() method,
     * we'll use a default value that will be updated by the import process
     *
     * @return int
     */
    private function getCurrentRowNumber(): int
    {
        // The actual row number will be set by the Laravel Excel import process
        // when the failure is processed. Using 0 as a placeholder.
        return 0;
    }
}

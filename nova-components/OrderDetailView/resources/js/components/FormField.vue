<template>
    <DefaultField
        :field="field"
        :errors="errors"
        :show-help-text="showHelpText"
        :full-width-content="fullWidthContent"
    >
        <template #field>
            <div class="odv-wrapper">
                <!-- Key-Value presentation (e.g., Shipping Method) -->
                <span v-if="field.value && field.value.kvp" style="width: 100%">
                    <div class="kvp-wrapper" v-for="(val, key) in value" :key="key">
                        <div>{{ key }}</div>
                        <div>{{ val }}</div>
                    </div>
                </span>

                <!-- Address/details display -->
                <span v-else style="width: 100%">
                    <div class="value-wrapper" v-for="(val, index) in value" :key="index" v-html="val"></div>
                </span>

                <!-- Edit actions -->
                <div v-if="isAddressEditable" class="action-wrapper">
                    <template v-if="!editing">
                        <p class="action-btn" @click.prevent="startEdit">Edit</p>
                    </template>
                    <template v-else>
                        <p class="action-btn" @click.prevent="cancelEdit">Cancel</p>
                        <p class="action-btn" :class="{ disabled: saving }" @click.prevent="saveEdit">
                            {{ saving ? 'Saving…' : 'Save' }}
                        </p>
                    </template>
                </div>

                <!-- Inline edit form for Shipping/Billing -->
                <div v-if="editing" class="edit-form">
                    <div v-if="isShipping" class="grid">
                        <input v-model="form.name" type="text" placeholder="Full name" />
                        <input v-model="form.phone" type="text" placeholder="Phone" />
                        <input v-model="form.address_line_1" type="text" placeholder="Address line 1 *" />
                        <input v-model="form.address_line_2" type="text" placeholder="Address line 2" />
                        <input v-model="form.city" type="text" placeholder="City *" />
                        <input v-model="form.state" type="text" placeholder="State/Province *" />
                        <input v-model="form.postal_code" type="text" placeholder="Postal code *" />
                        <input v-model="form.country" type="text" placeholder="Country *" />
                    </div>
                    <div v-else-if="isBilling" class="grid">
                        <input v-model="form.name" type="text" placeholder="Full name" />
                        <input v-model="form.address_line_1" type="text" placeholder="Address line 1 *" />
                        <input v-model="form.address_line_2" type="text" placeholder="Address line 2" />
                        <input v-model="form.city" type="text" placeholder="City *" />
                        <input v-model="form.state" type="text" placeholder="State/Province *" />
                        <input v-model="form.postal_code" type="text" placeholder="Postal code *" />
                        <input v-model="form.country" type="text" placeholder="Country *" />
                    </div>

                    <!-- Client-side validation errors -->
                    <ul v-if="frontErrors.length" class="errors">
                        <li v-for="(e, i) in frontErrors" :key="i">{{ e }}</li>
                    </ul>
                </div>
            </div>
        </template>
    </DefaultField>
</template>

<script>
import { FormField, HandlesValidationErrors } from 'laravel-nova'

export default {
    mixins: [FormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field'],
    data() {
        return {
            value: '',
            editing: false,
            saving: false,
            form: {
                name: '',
                phone: '',
                address_line_1: '',
                address_line_2: '',
                city: '',
                state: '',
                postal_code: '',
                country: '',
            },
            frontErrors: [],
        }
    },
    computed: {
        isShipping() {
            // Only the field explicitly labeled "Shipping" is the editable address
            return this.field.name === 'Shipping'
        },
        isBilling() {
            // Only the field explicitly labeled "Billing" is the editable billing address
            return this.field.name === 'Billing'
        },
        isAddressEditable() {
            // Also ensure it's not a key-value (like Shipping Method)
            const isKvp = this.field.value && this.field.value.kvp
            return (this.isShipping || this.isBilling) && !isKvp
        },
    },
    methods: {
        setInitialValue() {
            this.value = this.field.value && this.field.value.kvp ? this.field.value.kvp : this.field.value
        },
        startEdit() {
            this.frontErrors = []
            this.editing = true
            const current = this.value || {}
            // When value is an array of strings (value-wrapper), reconstruct from field.value
            const src = this.field.value && !this.field.value.kvp ? this.field.value : current
            this.form = {
                name: src.name || '',
                phone: src.phone || '',
                address_line_1: src.address_line_1 || '',
                address_line_2: src.address_line_2 || '',
                city: src.city || '',
                state: src.state || '',
                postal_code: src.postal_code || '',
                country: src.country || '',
            }
        },
        cancelEdit() {
            this.editing = false
            this.frontErrors = []
        },
        validateForm() {
            const errs = []
            if (!this.form.address_line_1) errs.push('Address line 1 is required')
            if (!this.form.city) errs.push('City is required')
            if (!this.form.state) errs.push('State/Province is required')
            if (!this.form.postal_code) errs.push('Postal code is required')
            if (!this.form.country) errs.push('Country is required')
            // For shipping, phone is optional but can be validated length if present
            this.frontErrors = errs
            return errs.length === 0
        },
        async saveEdit() {
            if (!this.validateForm()) return
            this.saving = true
            try {
                const payload = {}
                if (this.isShipping) {
                    payload.shipping = { ...this.form }
                } else if (this.isBilling) {
                    // Billing updates live under payments.creditInfo on the server
                    payload.payments = { ...this.form }
                }

                await Nova.request().post(`/nova-custom-api/orders/update/${this.resourceId}`, payload)

                Nova.success('Address updated')
                // Update local display value
                this.value = { ...this.form }
                this.editing = false
            } catch (e) {
                const message = e?.response?.data?.message || 'Failed to update address'
                Nova.error(message)
                // If validation errors were returned, surface them
                const errs = e?.response?.data?.errors
                if (errs) {
                    this.frontErrors = Object.values(errs).flat()
                }
            } finally {
                this.saving = false
            }
        },
    },
}
</script>

<style>
.odv-wrapper {
    display: flex;
    flex-direction: column;
}
.action-wrapper {
    display: flex;
    margin-top: 8px;
}
.action-btn {
    color: #4099de;
    cursor: pointer;
    font-size: 15px;
    margin-right: 16px;
}
.action-btn.disabled {
    opacity: 0.6;
    pointer-events: none;
}
.value-wrapper {
    color: #525860;
    font-size: 16px;
    margin-bottom: 4px;
}
.kvp-wrapper {
    display: grid;
    grid-template-columns: 150px 1fr;
    column-gap: 12px;
    margin-bottom: 4px;
}
.edit-form {
    margin-top: 12px;
}
.grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 8px 12px;
}
.grid input {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px 10px;
    font-size: 14px;
}
.errors {
    color: #dc2626;
    margin-top: 8px;
}
</style>

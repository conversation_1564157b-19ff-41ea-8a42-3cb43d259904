export function onSuccess(response) {
    const { data } = response;
    try {
        if (typeof data === 'string') {
            if (
                data.startsWith('<!DOCTYPE') ||
                data.startsWith('<!doctype') ||
                data.startsWith('<html')
            ) {
                console.log(response);
                if (Eichlers != 'undefined') {
                    Eichlers.$proxyWarning.init();
                }
            }
        }
    } catch (error) {
        console.log(error);
    }
    return response;
}

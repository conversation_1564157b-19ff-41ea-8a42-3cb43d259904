import Messaging from '../components/partials/Messaging';
import MobileScrollUp from '../components/layouts/MobileScrollUp';
import Ffooter from '../components/footer/Ffooter';
import LayoutsHeader from '../components/layouts/LayoutsHeader';
import LoadingSpinner from '../components/partials/LoadingSpinner';
import SelectBox from '../components/partials/SelectBox.vue';
import CustomSelect from '../components/partials/CustomSelect';
import CustomInput from '../components/partials/CustomInput';
import CurrencyField from '../components/footer/CurrencyField';
import InlineLoader from '../components/partials/InlineLoader';

export function register(Vue) {
    Vue.component('messaging', Messaging);
    Vue.component('mobile-scroll-up', MobileScrollUp);
    Vue.component('ffooter', Ffooter);
    Vue.component('layouts-header', LayoutsHeader);
    Vue.component('loading-spinner', LoadingSpinner);
    Vue.component('select-box', SelectBox);
    Vue.component('custom-select', CustomSelect);
    Vue.component('custom-input', CustomInput);
    Vue.component('currency-field', CurrencyField);
    Vue.component('inline-loader', InlineLoader);
}

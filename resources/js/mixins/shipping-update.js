import store from '../store';

export function ShippingUpdate(response) {
    try {
        if (_.get(response.headers, 'shipping_updated_at')) {
            if (
                store.getters['shippingRates/serverExpires'] !=
                _.get(response.headers, 'shipping_updated_at')
            ) {
                let serverTime = new Date(
                    _.get(response.headers, 'shipping_updated_at')
                );
                let localTime = new Date(
                    store.getters['shippingRates/serverExpires']
                );
                if (serverTime > localTime || localTime == 'Invalid Date') {
                    store.commit(
                        'shippingRates/SET_SERVER_EXPIRE',
                        _.get(response.headers, 'shipping_updated_at')
                    );
                }
            }
        }
    } catch (error) {
        console.log(error);
    }
    return response;
}

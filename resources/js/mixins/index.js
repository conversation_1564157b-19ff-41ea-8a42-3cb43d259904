import accounting from 'accounting';
import moment from 'moment';
import cssVars from 'css-vars-ponyfill';

function currency(value, decimal = 2) {
    return '$' + accounting.formatNumber(value, decimal);
}

export function registerFilters(Vue) {
    Vue.filter('currency', function (val) {
        return currency(val);
    });
    Vue.filter('priceIsGreater', function (val) {
        return +val > 0 ? currency(val) : 'Free';
    });
    Vue.filter('shortDate', function (value) {
        if (!value) return '';
        let d = new Date(value);
        let options = { year: 'short', month: 'short' };
        return d.toLocaleDateString('en-US', options);
    });
    Vue.filter('longDate', function (value) {
        if (!value) return '';
        return moment(value).format('dddd, MMMM D, YYYY');
    });
    Vue.filter('mmddyyyy', function (value) {
        if (!value) return '';
        return moment(value).format('MMM D, YYYY');
    });
    Vue.filter('MMddyyyy', function (value) {
        if (!value) return '';
        return moment(value).format('MMMM D, YYYY');
    });
    Vue.filter('DDMMdd', function (value) {
        if (!value) return '';
        return moment(value).format('dddd, MMMM D');
    });
    Vue.filter('DMMdd', function (value) {
        if (!value) return '';
        return moment(value).format('ddd, MMMM D');
    });
}

export function processCssVars() {
    cssVars({
        include: 'style',
        onlyLegacy: true,
        // onComplete(cssText, styleElms, cssVariables, benchmark) {
        //   console.log(cssVariables)
        // }
    });
}

import Reflect from 'core-js/es6/reflect';
export function processPolyfills() {
    // polyfill for findIndex
    if (!Array.prototype.findIndex) {
        Object.defineProperty(Array.prototype, 'findIndex', {
            value: function (predicate) {
                // 1. Let O be ? ToObject(this value).
                if (this == null) {
                    throw new TypeError('"this" is null or not defined');
                }

                var o = Object(this);

                // 2. Let len be ? ToLength(? Get(O, "length")).
                var len = o.length >>> 0;

                // 3. If IsCallable(predicate) is false, throw a TypeError exception.
                if (typeof predicate !== 'function') {
                    throw new TypeError('predicate must be a function');
                }

                // 4. If thisArg was supplied, let T be thisArg; else let T be undefined.
                var thisArg = arguments[1];

                // 5. Let k be 0.
                var k = 0;

                // 6. Repeat, while k < len
                while (k < len) {
                    // a. Let Pk be ! ToString(k).
                    // b. Let kValue be ? Get(O, Pk).
                    // c. Let testResult be ToBoolean(? Call(predicate, T, « kValue, k, O »)).
                    // d. If testResult is true, return k.
                    var kValue = o[k];
                    if (predicate.call(thisArg, kValue, k, o)) {
                        return k;
                    }
                    // e. Increase k by 1.
                    k++;
                }

                // 7. Return -1.
                return -1;
            },
            configurable: true,
            writable: true,
        });
    }

    //polyfill for array includes
    if (!Array.prototype.includes) {
        Object.defineProperty(Array.prototype, 'includes', {
            configurable: true,
            value: function includes(searchElement /*, fromIndex*/) {
                'use strict';
                var O = Object(this);
                var len = parseInt(O.length) || 0;
                if (len === 0) {
                    return false;
                }
                var n = parseInt(arguments[1]) || 0;
                var k;
                if (n >= 0) {
                    k = n;
                } else {
                    k = len + n;
                    if (k < 0) {
                        k = 0;
                    }
                }
                var currentElement;
                while (k < len) {
                    currentElement = O[k];
                    if (
                        searchElement === currentElement ||
                        (searchElement !== searchElement &&
                            currentElement !== currentElement)
                    ) {
                        return true;
                    }
                    k++;
                }
                return false;
            },
            writable: true,
        });
    }

    //polyfill for array find
    if (!Array.prototype.find) {
        Object.defineProperty(Array.prototype, 'find', {
            value: function (predicate) {
                // 1. Let O be ? ToObject(this value).
                if (this == null) {
                    throw TypeError('"this" is null or not defined');
                }
                var o = Object(this);
                // 2. Let len be ? ToLength(? Get(O, "length")).
                var len = o.length >>> 0;
                // 3. If IsCallable(predicate) is false, throw a TypeError exception.
                if (typeof predicate !== 'function') {
                    throw TypeError('predicate must be a function');
                }
                // 4. If thisArg was supplied, let T be thisArg; else let T be undefined.
                var thisArg = arguments[1];
                // 5. Let k be 0.
                var k = 0;
                // 6. Repeat, while k < len
                while (k < len) {
                    // a. Let Pk be ! ToString(k).
                    // b. Let kValue be ? Get(O, Pk).
                    // c. Let testResult be ToBoolean(? Call(predicate, T, « kValue, k, O »)).
                    // d. If testResult is true, return kValue.
                    var kValue = o[k];
                    if (predicate.call(thisArg, kValue, k, o)) {
                        return kValue;
                    }
                    // e. Increase k by 1.
                    k++;
                }
                // 7. Return undefined.
                return undefined;
            },
            configurable: true,
            writable: true,
        });
    }

    //polyfill for  Object.keys
    if (!Object.keys) {
        Object.keys = (function () {
            'use strict';
            var hasOwnProperty = Object.prototype.hasOwnProperty,
                hasDontEnumBug = !{ toString: null }.propertyIsEnumerable(
                    'toString'
                ),
                dontEnums = [
                    'toString',
                    'toLocaleString',
                    'valueOf',
                    'hasOwnProperty',
                    'isPrototypeOf',
                    'propertyIsEnumerable',
                    'constructor',
                ],
                dontEnumsLength = dontEnums.length;

            return function (obj) {
                if (
                    typeof obj !== 'function' &&
                    (typeof obj !== 'object' || obj === null)
                ) {
                    throw new TypeError('Object.keys called on non-object');
                }

                var result = [],
                    prop,
                    i;

                for (prop in obj) {
                    if (hasOwnProperty.call(obj, prop)) {
                        result.push(prop);
                    }
                }

                if (hasDontEnumBug) {
                    for (i = 0; i < dontEnumsLength; i++) {
                        if (hasOwnProperty.call(obj, dontEnums[i])) {
                            result.push(dontEnums[i]);
                        }
                    }
                }
                return result;
            };
        })();
    }

    //polyfill for  Object.entries
    if (!Object.entries) {
        Object.entries = function (obj) {
            var ownProps = Object.keys(obj),
                i = ownProps.length,
                resArray = new Array(i); // preallocate the Array
            while (i--) resArray[i] = [ownProps[i], obj[ownProps[i]]];

            return resArray;
        };
    }

    //polyfill for Object.values && Object.entries
    // const reduce = Function.bind.call(Function.call, Array.prototype.reduce);
    // const isEnumerable = Function.bind.call(Function.call, Object.prototype.propertyIsEnumerable);
    // const concat = Function.bind.call(Function.call, Array.prototype.concat);
    // const keys = Reflect.ownKeys;

    // if (!Object.values) {
    //   Object.values = function values(O) {
    //     return reduce(keys(O), (v, k) => concat(v, typeof k === 'string' && isEnumerable(O, k) ? [O[k]] : []), []);
    //   };
    // }

    // if (!Object.entries) {
    //   Object.entries = function entries(O) {
    //     return reduce(keys(O), (e, k) => concat(e, typeof k === 'string' && isEnumerable(O, k) ? [[k, O[k]]] : []), []);
    //   };
    // }
}

export function gtag() {
    dataLayer.push(arguments);
}

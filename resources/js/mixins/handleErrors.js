import Bugsnag from '@bugsnag/js';

export function handleError(err, router) {
    if (_.get(err, 'response')) {
        switch (err.response.status) {
            case 404:
                router.push({ name: '404' }).catch((err) => {});
                break;
            case 403:
                router.push({ name: '404' }).catch((err) => {});
                break;
            case 500:
                router.push({ name: 'ServerError' }).catch((err) => {});
                break;
            case 302:
                router
                    .replace({ path: err.response.data.message })
                    .catch((err) => {});
                break;
            default:
                Bugsnag.notify(err);
        }
    }
}

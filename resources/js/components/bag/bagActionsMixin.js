import { mapActions } from 'vuex';
export default {
    methods: {
        ...mapActions([
            'removeProduct',
            'removeFromLater',
            'addToLater',
            'fromLater',
            'addToCart',
        ]),

        updateQuantity(type, product, event) {
            if (parseInt(event) > 0) {
                if (type == 'cart') {
                    this.addToCart({
                        ...product,
                        quantity: parseInt(event),
                        isUpdate: true,
                    }).then(() => {});
                } else {
                    this.addToLater({ ...product, quantity: parseInt(event) });
                }
            }
        },

        _removeProduct(item, ref) {
            this.$refs[ref][0].toggle();
            this.removeProduct(item).then(() => {
                if (this.$refs[ref][0]) this.$refs[ref][0].toggle();
            });
        },

        _addToLater(item, ref) {
            this.$refs[ref][0].toggle();
            this.addToLater(item).then(() => {
                if (this.$refs[ref][0]) this.$refs[ref][0].toggle();
            });
        },

        _removeFromLater(item, ref) {
            this.$refs[ref][0].toggle();
            this.removeFromLater(item).then(() => {
                if (this.$refs[ref][0]) this.$refs[ref][0].toggle();
            });
        },

        _fromLater(item, ref) {
            this.$refs[ref][0].toggle();
            this.fromLater(item).then(() => {
                if (this.$refs[ref][0]) this.$refs[ref][0].toggle();
            });
        },
    },
};

import { mapGetters } from 'vuex';
export default {
    computed: {
        ...mapGetters(['products']),

        quantityAlreadyInBag() {
            return (product, id, type) => {
                let products = this.products.filter(
                    (p) => p.id == id && p.type == type
                );

                if (products.length > 0) {
                    return products
                        .map((p) => p.quantity)
                        .reduce((prev, curr) => +prev + +curr, 0);
                }
                return 0;
            };
        },

        maxQuantityReached() {
            return (product, id, type) => {
                if (!product.max) return;

                return (
                    this.quantityAlreadyInBag(product, id, type) >= product.max
                );
            };
        },
    },
};

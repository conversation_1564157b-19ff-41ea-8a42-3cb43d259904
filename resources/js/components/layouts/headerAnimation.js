var interval;

export default class HeaderAnimations {
    constructor(isBeta) {
        this.navUpClass = isBeta ? 'beta-nav-up' : 'nav-up';
        this.didScroll;
        this.lastScrollTop = 0;
        this.delta = 5;
        this.navbarHeight = $('header').outerHeight();

        window.addEventListener('scroll', () => {
            this.didScroll = true;
        });

        this.interval();
    }

    interval() {
        interval = setInterval(() => {
            if (this.didScroll) {
                this.hasScrolled();
                this.didScroll = false;
            }
        }, 250);
    }

    clearInterval() {
        clearInterval(interval);
    }

    hasScrolled() {
        var st = window.scrollY;

        // Make sure they scroll more than delta
        if (Math.abs(this.lastScrollTop - st) <= this.delta) return;

        // If they scrolled down and are past the navbar, add class .nav-up.
        // This is necessary so you never see what is "behind" the navbar.
        if (st > this.lastScrollTop && st > this.navbarHeight) {
            // Scroll Down
            document.querySelector('header').classList.remove('nav-down');
            document.querySelector('header').classList.add(this.navUpClass);
            // $(document).find('.large_product_images').removeClass('lpi_down');
        } else {
            // Scroll Up
            if (st + $(window).height() < $(document).height()) {
                document
                    .querySelector('header')
                    .classList.remove(this.navUpClass);
                document.querySelector('header').classList.add('nav-down');
                //$(document).find('.large_product_images').addClass('lpi_down');
            }
        }

        this.lastScrollTop = st;
    }
}

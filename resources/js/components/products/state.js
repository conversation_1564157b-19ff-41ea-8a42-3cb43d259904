export default {
    data: {
        product: {},
        variation: {},
        related: [],
        hebrewProducts: false,
        hebrewMode: false,
        englishMode: false,
        localId: null,
        personalization: null,
    },
    clearState() {
        this.data.product = {};
        this.data.variation = {};
        this.data.related = [];
        this.data.hebrewProducts = false;
        this.data.localId = null;
        this.data.personalization = null;
    },
    setProduct(product) {
        this.data.product = product;
        if (product.variation_infos && product.variation_infos.length) {
            this.data.variation = product.variation_infos[0];
        } else {
            this.data.variation = {};
        }

        if (
            this.data.product.heb_title ||
            this.data.product.heb_description ||
            this.data.product.heb_short_desc
        ) {
            this.data.hebrewProducts = true;
        }
    },
    getCurrentLang() {
        let lang = localStorage.getItem('lang');
        this.data.hebrewMode = lang == 'heb' && this.getHebrewOption();
        this.data.englishMode = !this.data.hebrewMode;
    },
    getHebrewOption() {
        return (
            this.getProduct().heb_title ||
            this.getProduct().heb_description ||
            this.getProduct().heb_short_desc != null ||
            this.data.hebrewProducts ||
            (this.getProduct().creators &&
                this.getProduct().creators.some((creator) => creator.heb_name))
        );
    },
    getProduct() {
        return this.data.product;
    },
    setVariation(variation) {
        this.data.variation = variation;
    },
    getVariation() {
        return this.data.variation;
    },
    setHebrewMode() {
        localStorage.setItem('lang', 'heb');
        this.data.hebrewMode = true;
        this.data.englishMode = false;
    },
    setEnglishMode() {
        localStorage.setItem('lang', 'eng');
        this.data.englishMode = true;
        this.data.hebrewMode = false;
    },
    setHebrewProducts() {
        this.data.hebrewProducts = true;
    },
    setLocalId(item_key = null) {
        this.data.localId = item_key
            ? item_key
            : '_' + Math.random().toString(36).substr(2, 9);
    },
    setPersonalization(personalization, item_key = null) {
        let isNull = true;
        for (let [key, value] of Object.entries(personalization)) {
            isNull = isNull && personalization[key].length <= 0;
        }
        this.data.personalization = isNull ? null : personalization;

        !isNull ? this.setLocalId(item_key) : (this.data.localId = null);
    },

    getCurrent() {
        if (this.data.product.type == 'giftCard') {
            return this.data.product;
        }

        let price = 0;
        let fake_price = 0;

        if (this.data.variation && this.data.variation.price != null) {
            price = this.data.variation.price;
        } else {
            price = this.data.product.price;
        }

        if (this.data.variation && this.data.variation.fake_price) {
            fake_price = this.data.variation.fake_price;
        } else {
            fake_price = this.data.product.fake_price;
        }
        let current = {
            title: this.data.product.title,
            vendor: _.get(this.data.product, 'vendor')
                ? this.data.product.vendor.name
                : null,

            path: this.data.product.path,
            preorder_date: this.data.variation.preorder_date ?? null,
            price: price,
            fakePrice: fake_price > price ? fake_price : 0,
            exclude_free_shipping: this.data.product.exclude_free_shipping,

            //for local
            item_key: this.data.localId,

            personalization: this.data.personalization,
            is_personalization: !!this.data.product.personalizations,
            // needed to track addOns & for discounts
            product_id: this.data.product.id,
        };
        if (
            _.get(this.data.product, 'media_urls') &&
            this.data.product.media_urls.length
        ) {
            current['media'] = this.data.variation.picture
                ? this.data.variation.picture.thumbnail
                : this.data.product.media_urls[0].thumbnail;
        } else if (_.get(this.data.product, 'media')) {
            current['media'] = this.data.product.media;
        } else {
            current['media'] = this.data.product.image;
        }
        if (_.get(this.data.variation, 'id')) {
            current['id'] = this.data.variation.id;
            current['type'] = 'variation';
            current['meta'] = this.data.variation.meta;
            current['item_type'] = this.data.variation.item_type;
            current['max'] = this.data.variation.max;
            current['max_quantity'] = this.data.variation.max_quantity;
            current['sku'] = this.data.variation.sku || this.data.product.sku;
        } else if (_.get(this.data.product, 'type') == 'variation') {
            current['id'] = this.data.product.id;
            current['type'] = 'variation';
            current['meta'] = this.data.product.meta;
            current['item_type'] = this.data.product.item_type;
            current['max'] = this.data.product.max;
            current['max_quantity'] = this.data.product.max_quantity;
            current['sku'] = this.data.product.sku;
        } else {
            current['id'] = this.data.product.id;
            current['type'] = this.data.product.type
                ? this.data.product.type
                : 'product';
            current['item_type'] = this.data.product.item_type;
            current['max'] = this.data.product.max;
            current['max_quantity'] = this.data.product.max_quantity;
            current['sku'] = this.data.product.sku;
        }
        return current;
    },
};

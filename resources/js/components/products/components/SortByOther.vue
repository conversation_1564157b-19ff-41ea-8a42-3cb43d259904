<template>
    <div v-if="show" class="usc_sort only-desktop">
        <span class="usc_sort_ttl">Sort by:</span>
        <select
            v-model="sortValue"
            @change="sort"
            style="font-size: 14px; padding-left: 5px"
        >
            <option :value="'relavent'">Most Relevant</option>
            <option :value="'price-asc'">Price: Low to High</option>
            <option :value="'price-desc'">Price: High to Low</option>
            <option :value="'release_date-desc'">Newest</option>
        </select>
    </div>
</template>

<script>
export default {
    data() {
        return {
            sortPrice: null,
            sortValue: '',
            hasSearchResults: true,
        };
    },
    computed: {
        show() {
            return (
                this.sortPrice &&
                this.hasSearchResults &&
                (this.$route.name === 'ProductsIndex' ||
                    this.$route.name === 'SearchIndex' ||
                    this.$route.name === 'automated-category') &&
                this.$route.name !== 'product-collection'
            );
        },
    },
    methods: {
        sort(e) {
            this.sortValue = e.target.value;
            let queryBuilder = this.sortPrice.sideBar.queryBuilder;

            // Remove page and sort from query builder
            delete queryBuilder['page'];
            delete queryBuilder['sort'];

            // Add sort parameter if not 'relavent'
            if (e.target.value !== 'relavent') {
                queryBuilder['sort'] = e.target.value;
            }

            // Reset to page 1
            queryBuilder['page'] = 1;

            // Update URL using router push
            this.$router.push({
                query: queryBuilder,
            }).catch(() => {});
        },
    },
    inject: ['componentsComunaction'],
    mounted() {
        this.componentsComunaction.$on('productsFinishedLoading', (e) => {
            this.sortPrice = e;
            this.componentsComunaction.$emit('sortMounted', this);
            if (_.get(this.$route.query, 'sort')) {
                this.sortValue = this.$route.query.sort;
                this.sortPrice.sideBar.queryBuilder['sort'] = this.sortValue;
            } else {
                this.sortValue = 'relavent';
            }
        });
        this.componentsComunaction.$on('hasSearchResults', (e) => {
            this.hasSearchResults = e;
        });
    },
    watch: {
        $route: {
            handler(val) {
                if (!_.get(this.$route.query, 'sort')) {
                    this.sortValue = 'relavent';
                }
            },
            deep: true,
        },
    },
};
</script>

<style scoped>
.only-desktop {
    display: block;
    font-size: 14px;
}
@media screen and (max-width: 1000px) {
    .only-desktop {
        display: none;
    }
}
</style>

import state from './state';
import { mapGetters } from 'vuex';

export default {
    data() {
        return {
            state,
        };
    },

    computed: {
        ...mapGetters({
            currentZip: 'zipLookUp/getZipCode',
        }),

        // Check if the current zip code matches the zip code used for product rates
        shouldUseExtendedRates() {
            // If no current zip code is set, don't use extended rates
            if (!this.currentZip) {
                return false;
            }

            // If no product rates exist, don't use extended rates
            if (!('rates' in this.state.data.product)) {
                return false;
            }

            // For now, we'll disable extended rates when a zip code is entered
            // to ensure the current shipping options based on the user's zip code take precedence
            // This fixes the bug where stale product rates override current shipping options
            return false;
        },

        extendedCheapest() {
            if (
                !this.shouldUseExtendedRates ||
                !_.get(this.state.data.product, 'rates.cheapest')
            )
                return null;

            const cheapest = this.state.data.product.rates.cheapest;
            return {
                id: cheapest.id,
                msg: `Get it ${this.$options.filters.DMMdd(
                    cheapest.estimated_arrival
                )}`,
                name: cheapest.name,
            };
        },

        extendedFastest() {
            if (
                !this.shouldUseExtendedRates ||
                !_.get(this.state.data.product, 'rates.fastest')
            )
                return null;

            const fastest = this.state.data.product.rates.fastest;
            return {
                id: fastest.id,
                msg: `Get it ${this.$options.filters.DMMdd(
                    fastest.estimated_arrival
                )}`,
                name: fastest.name,
            };
        },

        extendedFreeOver() {
            if (
                !this.shouldUseExtendedRates ||
                !_.get(this.state.data.product, 'rates.free_over')
            )
                return null;

            const free_over = this.state.data.product.rates.free_over;
            return {
                id: free_over.id,
                msg: `Get it ${this.$options.filters.DMMdd(
                    free_over.estimated_arrival
                )}`,
                name: free_over.name,
                over: '$' + free_over.free_shipping_above,
            };
        },

        extendedPickup() {
            if (
                !this.shouldUseExtendedRates ||
                !_.get(this.state.data.product, 'rates.pickup')
            )
                return null;

            return this.state.data.product.rates.pickup.date;
        },
    },
};

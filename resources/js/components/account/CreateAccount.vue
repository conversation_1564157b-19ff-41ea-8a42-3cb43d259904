<template>
    <div class="create-wrapper">
        <p class="ttl">Create an Account</p>

        <!-- name -->
        <div class="pcl_input_set" :style="{ 'margin-bottom': '1px' }">
            <input
                type="text"
                name="name"
                class="pcl_input_text"
                placeholder="Full Name"
                autocomplete="name"
                v-model="$v.cred.name.$model"
                :class="{ error: $v.cred.name.$invalid && $v.cred.name.$dirty }"
            />
        </div>
        <div class="small">
            <small
                v-show="$v.cred.name.$dirty && !$v.cred.name.required"
                class="error-msg"
                >Name is a required field</small
            >
        </div>

        <!-- email -->
        <div class="pcl_input_set" :style="{ 'margin-bottom': '1px' }">
            <input
                type="text"
                name=""
                class="pcl_input_text"
                placeholder="Email Address"
                v-model.trim="$v.cred.email.$model"
                :class="{
                    error: $v.cred.email.$invalid && $v.cred.email.$dirty,
                }"
                @blur="emailError = true"
            />
        </div>
        <div class="small">
            <small
                v-show="$v.cred.email.$dirty && !$v.cred.email.required"
                class="error-msg"
                >Email is required</small
            >
            <small v-show="emailError && !$v.cred.email.email" class="error-msg"
                >Invalid Email Address</small
            >
            <small v-if="errors.email" class="error-msg">{{
                errors.email[0]
            }}</small>
        </div>

        <!-- password -->
        <div class="pcl_input_set" :style="{ 'margin-bottom': '1px' }">
            <input
                type="password"
                name="password"
                class="pcl_input_text"
                placeholder="Password"
                autocomplete="password"
                v-model.trim="$v.cred.password.$model"
                :class="{
                    error: $v.cred.password.$invalid && $v.cred.password.$dirty,
                }"
                @blur="minError = true"
            />
        </div>
        <div class="small">
            <small
                v-show="$v.cred.password.$dirty && !$v.cred.password.required"
                class="error-msg"
                >Password is required</small
            >
            <small
                v-show="minError && !$v.cred.password.minLength"
                class="error-msg"
                >Password must have a minimum of
                {{ $v.cred.password.$params.minLength.min }} characters</small
            >
        </div>

        <!-- password_confirmation -->
        <div class="pcl_input_set" :style="{ 'margin-bottom': '1px' }">
            <input
                type="password"
                name="password"
                class="pcl_input_text"
                placeholder="Confirm Password"
                autocomplete="password"
                v-model.trim="$v.cred.password_confirmation.$model"
                :class="{
                    error:
                        $v.cred.password_confirmation.$invalid &&
                        $v.cred.password_confirmation.$dirty,
                }"
                @blur="sameAsPasswordError = true"
            />
        </div>
        <div class="small">
            <small
                v-show="
                    $v.cred.password_confirmation.$dirty &&
                    !$v.cred.password_confirmation.required
                "
                class="error-msg"
                >Please Re-enter your password</small
            >
            <small
                v-show="
                    sameAsPasswordError &&
                    !$v.cred.password_confirmation.sameAsPassword
                "
                class="error-msg"
                >Password does not match</small
            >
        </div>

        <p class="terms">
            By continuing you agree to our
            <a href="/terms-of-use/39">Terms and Conditions,</a>our
            <a href="/privacy-policy/40">Privacy Policy,</a>
        </p>

        <p @click="create()" class="no_account_cont">Create Account</p>

        <p class="sign-in">
            or <router-link :to="{ name: 'Login' }">Sign in</router-link>
        </p>
    </div>
</template>

<script>
import { required, email, minLength, sameAs } from 'vuelidate/lib/validators';
import { mapActions } from 'vuex';
import { initRecaptcha, recaptchaToken } from '../checkout/recaptcha';
export default {
    data() {
        return {
            errors: {},
            emailError: false,
            minError: false,
            sameAsPasswordError: false,
            recaptchaLoaded: false,
            cred: {
                name: '',
                email: '',
                password: '',
                password_confirmation: '',
            },
        };
    },
    methods: {
        ...mapActions({
            createCustomer: 'customer/createCustomer',
        }),
        async create() {
            while (!this.recaptchaLoaded) {}
            let recaptcha_token;
            try {
                recaptcha_token = await recaptchaToken();
            } catch (error) {
                console.error(error);
            }
            this.$v.cred.$touch();
            if (!this.$v.cred.$invalid) {
                this.$root.$refs.spinner.startSpinner();
                const payload = { ...this.cred, ...{ recaptcha_token } };
                axios
                    .post('/api/customer/create', payload)
                    .then((resp) => {
                        this.$root.$refs.spinner.stopSpinner();
                        this.createCustomer(resp.data);
                        let prevUrl = localStorage.getItem('prevUrl');
                        localStorage.removeItem('prevUrl');
                        this.$router.push({ path: prevUrl ? prevUrl : '/' });
                    })
                    .catch((err) => {
                        this.$root.$refs.spinner.stopSpinner();
                        if (err.response.status == 422) {
                            this.errors = err.response.data.errors;
                        } else {
                            handleError(err, this.$router);
                        }
                    });
            }
        },
    },
    mounted() {
        initRecaptcha().then((res) => (this.recaptchaLoaded = res));
    },

    validations: {
        cred: {
            name: { required },
            email: { required, email },
            password: { required, minLength: minLength(8) },
            password_confirmation: {
                required,
                sameAsPassword: sameAs('password'),
            },
        },
    },
};
</script>

<style scoped>
.create-wrapper {
    max-width: 490px;
    width: 100%;
    display: flex;
    flex-direction: column;
    margin: auto;
    margin-top: 50px;
    margin-bottom: 50px;
    padding: 20px;
}
.ttl {
    font-size: 26px;
    font-weight: 500;
    color: black;
    margin-bottom: 20px;
    margin-top: 40px;
}
.terms {
    font-size: 13px;
    font-weight: 300;
    color: black;
}
.terms a,
.sign-in a {
    color: #016145;
}
.sign-in {
    text-align: center;
    font-size: 23px;
    font-weight: 500;
    color: black;
}
.error {
    border-color: #f66;
}
.small {
    min-height: 16px !important;
    display: flex;
    flex-direction: column;
}
.error-msg {
    padding-bottom: 8px;
    padding-top: 4px;
    color: #f66;
}
</style>

<template>
    <span>
        <!-- light-boxes -->
        <update-profile ref="updateProfile" />
        <add-shipping ref="addShipping" />
        <add-payment ref="addPayment" />
        <edit-payment ref="updatePayment" />
        <div class="user_area_wrapper">
            <div class="user_sidebar">
                <router-link :to="{ name: 'Dashboard' }" class="us_link"
                    >My Account</router-link
                >
                <div class="us_links_inside">
                    <router-link to="/account/dashboard#profile" class="us_link"
                        >Profile</router-link
                    >
                    <router-link
                        to="/account/dashboard#shipping"
                        class="us_link"
                        >Shipping Addresses</router-link
                    >
                    <router-link to="/account/dashboard#payment" class="us_link"
                        >Payment Methods</router-link
                    >
                    <!-- <a  href="#payment" class="us_link">Payment Methods</a> -->
                </div>
                <router-link :to="{ name: 'Orders' }" class="us_link" router
                    >My Orders</router-link
                >
                <router-link :to="{ name: 'Favorites' }" class="us_link"
                    >My Favorites</router-link
                >
                <router-link :to="{ name: 'BueAgain' }" class="us_link"
                    >Buy it Again</router-link
                >
                <router-link :to="{ name: 'Subscriptions' }" class="us_link"
                    >Subscriptions</router-link
                >
                <router-link :to="{ name: 'DigitalLibary' }" class="us_link"
                    >Digital Libary</router-link
                >
                <router-link :to="{ name: 'ContactUs' }" class="us_link"
                    >Help</router-link
                >
            </div>
            <router-view />
        </div>
    </span>
</template>

<script>
import UpdateProfile from './UpdateProfile';
import addShipping from './AddShipping';
import AddPayment from './AddPayment';
import EditPayment from './EditPayment';
export default {
    components: {
        UpdateProfile,
        addShipping, //}
        AddPayment,
        EditPayment,
    },
};
</script>

<style scoped>
.router-link-exact-active {
    color: var(--darkgreen);
}
.active {
    color: var(--darkgreen);
}
</style>

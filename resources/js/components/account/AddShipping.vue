<template>
    <transition
        enter-active-class="fadeIn faster animated"
        leave-active-class="fadeOut faster animated"
    >
        <div v-if="show" class="account_popup wrepper">
            <div class="account_popup_ex">
                <img @click="close()" src="/img/ex_grey.svg" />
            </div>
            <div class="account_popup_inner">
                <form>
                    <p class="checkout_card_ttl">
                        {{ edit ? 'Edit' : 'Add' }} Shipping Address
                    </p>
                    <!-- Full Name -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Full Name"
                            v-model.trim="$v.address.name.$model"
                            :class="{
                                error:
                                    $v.address.name.$invalid &&
                                    $v.address.name.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.address.name.$dirty &&
                                !$v.address.name.required
                            "
                            class="error-msg"
                            >Name is required</small
                        >
                    </div>

                    <!-- Street Address -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Street Address"
                            v-model.trim="$v.address.address_line_1.$model"
                            :class="{
                                error:
                                    $v.address.address_line_1.$invalid &&
                                    $v.address.address_line_1.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.address.address_line_1.$dirty &&
                                !$v.address.address_line_1.required
                            "
                            class="error-msg"
                            >Address is required</small
                        >
                    </div>

                    <!-- Apt., Floor, Unit etc. -->
                    <div class="pcl_input_set">
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Apt., Floor, Unit etc."
                            v-model.trim="address.address_line_2"
                        />
                    </div>

                    <!-- country -->
                    <div
                        class="bir_qty_box"
                        style="width: 100%; margin-bottom: 16px"
                        :class="{
                            error:
                                $v.address.country.$invalid &&
                                $v.address.country.$dirty,
                        }"
                    >
                        <select
                            v-model="$v.address.country.$model"
                            class="bir_qty"
                            style="font-size: 17px"
                        >
                            <option selected disabled value="">
                                Select your Country
                            </option>
                            <option
                                v-for="country in countries"
                                :key="country.value"
                                :value="country.value"
                            >
                                {{ country.label }}
                            </option>
                        </select>
                    </div>

                    <!-- State -->
                    <div
                        v-if="address.country == 'US'"
                        class="bir_qty_box"
                        style="width: 100%"
                        :class="{
                            error:
                                $v.address.state.$invalid &&
                                $v.address.state.$dirty,
                        }"
                    >
                        <select
                            v-model="$v.address.state.$model"
                            class="bir_qty"
                            style="font-size: 17px"
                            autocomplete="shipping region"
                            name="ship-state"
                        >
                            <option selected disabled value="">
                                Select your State
                            </option>
                            <option
                                v-for="(state, index) in states"
                                :key="index"
                                :value="state.abbreviation"
                            >
                                {{ state.name }}
                            </option>
                        </select>
                    </div>

                    <!-- State -->
                    <div
                        v-else
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name="ship-state"
                            class="pcl_input_text"
                            placeholder="State/Province"
                            autocomplete="shipping region"
                            v-model.trim="$v.address.state.$model"
                            :class="{
                                error:
                                    $v.address.state.$invalid &&
                                    $v.address.state.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.address.state.$dirty &&
                                !$v.address.state.required
                            "
                            class="error-msg"
                            >State is required</small
                        >
                    </div>

                    <!-- City Code -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="City"
                            v-model.trim="$v.address.city.$model"
                            :class="{
                                error:
                                    $v.address.city.$invalid &&
                                    $v.address.city.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.address.city.$dirty &&
                                !$v.address.city.required
                            "
                            class="error-msg"
                            >City is required</small
                        >
                    </div>

                    <!-- Zip Code -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Zip Code"
                            v-model.trim="$v.address.postal_code.$model"
                            :class="{
                                error:
                                    $v.address.postal_code.$invalid &&
                                    $v.address.postal_code.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.address.postal_code.$dirty &&
                                !$v.address.postal_code.required
                            "
                            class="error-msg"
                            >Zip code is required</small
                        >
                    </div>

                    <!-- Phone Number -->
                    <div class="pcl_input_set">
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Phone Number"
                            v-model.trim="address.phone"
                        />
                    </div>

                    <div class="checkout_cb_box ccb_offers">
                        <div class="ccb_left">
                            <input
                                type="checkbox"
                                name=""
                                class="checkout_cb"
                                id="ccb_1"
                                v-model.trim="address.default"
                            />
                            <label class="checkout_cb_vis" for="ccb_1"></label>
                            <label class="checkout_label" for="ccb_1"
                                >Set a preferred address</label
                            >
                        </div>
                    </div>

                    <p class="no_account_cont" @click="Continue()">Continue</p>
                </form>
            </div>
        </div>
    </transition>
</template>

<script>
const a = {
    name: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    phone: '',
    default: false,
};
import { mapActions, mapGetters } from 'vuex';
import { required, integer } from 'vuelidate/lib/validators';
import ErrorHandling from './errorHandling';
import states from '../checkout/states';
export default {
    data() {
        return {
            edit: false,
            createMsg: 'Your Address was successful added!',
            editMsg: 'Your Address was successful updated!',
            show: false,
            address: {
                name: '',
                address_line_1: '',
                address_line_2: '',
                city: '',
                state: '',
                postal_code: '',
                country: '',
                phone: '',
                default: false,
            },
            //countries:[{"label":"Afghanistan","value":"AF"},{"label":"Aland Islands","value":"AX"},{"label":"Albania","value":"AL"},{"label":"Algeria","value":"DZ"},{"label":"American Samoa","value":"AS"},{"label":"Andorra","value":"AD"},{"label":"Angola","value":"AO"},{"label":"Anguilla","value":"AI"},{"label":"Antarctica","value":"AQ"},{"label":"Antigua And Barbuda","value":"AG"},{"label":"Argentina","value":"AR"},{"label":"Armenia","value":"AM"},{"label":"Aruba","value":"AW"},{"label":"Australia","value":"AU"},{"label":"Austria","value":"AT"},{"label":"Azerbaijan","value":"AZ"},{"label":"Bahamas","value":"BS"},{"label":"Bahrain","value":"BH"},{"label":"Bangladesh","value":"BD"},{"label":"Barbados","value":"BB"},{"label":"Belarus","value":"BY"},{"label":"Belgium","value":"BE"},{"label":"Belize","value":"BZ"},{"label":"Benin","value":"BJ"},{"label":"Bermuda","value":"BM"},{"label":"Bhutan","value":"BT"},{"label":"Bolivia","value":"BO"},{"label":"Bosnia And Herzegovina","value":"BA"},{"label":"Botswana","value":"BW"},{"label":"Bouvet Island","value":"BV"},{"label":"Brazil","value":"BR"},{"label":"British Indian Ocean Territory","value":"IO"},{"label":"Brunei Darussalam","value":"BN"},{"label":"Bulgaria","value":"BG"},{"label":"Burkina Faso","value":"BF"},{"label":"Burundi","value":"BI"},{"label":"Cambodia","value":"KH"},{"label":"Cameroon","value":"CM"},{"label":"Canada","value":"CA"},{"label":"Cape Verde","value":"CV"},{"label":"Cayman Islands","value":"KY"},{"label":"Central African Republic","value":"CF"},{"label":"Chad","value":"TD"},{"label":"Chile","value":"CL"},{"label":"China","value":"CN"},{"label":"Christmas Island","value":"CX"},{"label":"Cocos (Keeling) Islands","value":"CC"},{"label":"Colombia","value":"CO"},{"label":"Comoros","value":"KM"},{"label":"Congo","value":"CG"},{"label":"Congo, Democratic Republic","value":"CD"},{"label":"Cook Islands","value":"CK"},{"label":"Costa Rica","value":"CR"},{"label":"Cote D'Ivoire","value":"CI"},{"label":"Croatia","value":"HR"},{"label":"Cuba","value":"CU"},{"label":"Cyprus","value":"CY"},{"label":"Czech Republic","value":"CZ"},{"label":"Denmark","value":"DK"},{"label":"Djibouti","value":"DJ"},{"label":"Dominica","value":"DM"},{"label":"Dominican Republic","value":"DO"},{"label":"Ecuador","value":"EC"},{"label":"Egypt","value":"EG"},{"label":"El Salvador","value":"SV"},{"label":"Equatorial Guinea","value":"GQ"},{"label":"Eritrea","value":"ER"},{"label":"Estonia","value":"EE"},{"label":"Ethiopia","value":"ET"},{"label":"Falkland Islands (Malvinas)","value":"FK"},{"label":"Faroe Islands","value":"FO"},{"label":"Fiji","value":"FJ"},{"label":"Finland","value":"FI"},{"label":"France","value":"FR"},{"label":"French Guiana","value":"GF"},{"label":"French Polynesia","value":"PF"},{"label":"French Southern Territories","value":"TF"},{"label":"Gabon","value":"GA"},{"label":"Gambia","value":"GM"},{"label":"Georgia","value":"GE"},{"label":"Germany","value":"DE"},{"label":"Ghana","value":"GH"},{"label":"Gibraltar","value":"GI"},{"label":"Greece","value":"GR"},{"label":"Greenland","value":"GL"},{"label":"Grenada","value":"GD"},{"label":"Guadeloupe","value":"GP"},{"label":"Guam","value":"GU"},{"label":"Guatemala","value":"GT"},{"label":"Guernsey","value":"GG"},{"label":"Guinea","value":"GN"},{"label":"Guinea-Bissau","value":"GW"},{"label":"Guyana","value":"GY"},{"label":"Haiti","value":"HT"},{"label":"Heard Island & Mcdonald Islands","value":"HM"},{"label":"Holy See (Vatican City State)","value":"VA"},{"label":"Honduras","value":"HN"},{"label":"Hong Kong","value":"HK"},{"label":"Hungary","value":"HU"},{"label":"Iceland","value":"IS"},{"label":"India","value":"IN"},{"label":"Indonesia","value":"ID"},{"label":"Iran, Islamic Republic Of","value":"IR"},{"label":"Iraq","value":"IQ"},{"label":"Ireland","value":"IE"},{"label":"Isle Of Man","value":"IM"},{"label":"Israel","value":"IL"},{"label":"Italy","value":"IT"},{"label":"Jamaica","value":"JM"},{"label":"Japan","value":"JP"},{"label":"Jersey","value":"JE"},{"label":"Jordan","value":"JO"},{"label":"Kazakhstan","value":"KZ"},{"label":"Kenya","value":"KE"},{"label":"Kiribati","value":"KI"},{"label":"Korea","value":"KR"},{"label":"Kosovo","value":"XK"},{"label":"Kuwait","value":"KW"},{"label":"Kyrgyzstan","value":"KG"},{"label":"Lao People's Democratic Republic","value":"LA"},{"label":"Latvia","value":"LV"},{"label":"Lebanon","value":"LB"},{"label":"Lesotho","value":"LS"},{"label":"Liberia","value":"LR"},{"label":"Libyan Arab Jamahiriya","value":"LY"},{"label":"Liechtenstein","value":"LI"},{"label":"Lithuania","value":"LT"},{"label":"Luxembourg","value":"LU"},{"label":"Macao","value":"MO"},{"label":"Macedonia","value":"MK"},{"label":"Madagascar","value":"MG"},{"label":"Malawi","value":"MW"},{"label":"Malaysia","value":"MY"},{"label":"Maldives","value":"MV"},{"label":"Mali","value":"ML"},{"label":"Malta","value":"MT"},{"label":"Marshall Islands","value":"MH"},{"label":"Martinique","value":"MQ"},{"label":"Mauritania","value":"MR"},{"label":"Mauritius","value":"MU"},{"label":"Mayotte","value":"YT"},{"label":"Mexico","value":"MX"},{"label":"Micronesia, Federated States Of","value":"FM"},{"label":"Moldova","value":"MD"},{"label":"Monaco","value":"MC"},{"label":"Mongolia","value":"MN"},{"label":"Montenegro","value":"ME"},{"label":"Montserrat","value":"MS"},{"label":"Morocco","value":"MA"},{"label":"Mozambique","value":"MZ"},{"label":"Myanmar","value":"MM"},{"label":"Namibia","value":"NA"},{"label":"Nauru","value":"NR"},{"label":"Nepal","value":"NP"},{"label":"Netherlands","value":"NL"},{"label":"Netherlands Antilles","value":"AN"},{"label":"New Caledonia","value":"NC"},{"label":"New Zealand","value":"NZ"},{"label":"Nicaragua","value":"NI"},{"label":"Niger","value":"NE"},{"label":"Nigeria","value":"NG"},{"label":"Niue","value":"NU"},{"label":"Norfolk Island","value":"NF"},{"label":"Northern Mariana Islands","value":"MP"},{"label":"Norway","value":"NO"},{"label":"Oman","value":"OM"},{"label":"Pakistan","value":"PK"},{"label":"Palau","value":"PW"},{"label":"Palestinian Territory, Occupied","value":"PS"},{"label":"Panama","value":"PA"},{"label":"Papua New Guinea","value":"PG"},{"label":"Paraguay","value":"PY"},{"label":"Peru","value":"PE"},{"label":"Philippines","value":"PH"},{"label":"Pitcairn","value":"PN"},{"label":"Poland","value":"PL"},{"label":"Portugal","value":"PT"},{"label":"Puerto Rico","value":"PR"},{"label":"Qatar","value":"QA"},{"label":"Reunion","value":"RE"},{"label":"Romania","value":"RO"},{"label":"Russian Federation","value":"RU"},{"label":"Rwanda","value":"RW"},{"label":"Saint Barthelemy","value":"BL"},{"label":"Saint Helena","value":"SH"},{"label":"Saint Kitts And Nevis","value":"KN"},{"label":"Saint Lucia","value":"LC"},{"label":"Saint Martin","value":"MF"},{"label":"Saint Pierre And Miquelon","value":"PM"},{"label":"Saint Vincent And Grenadines","value":"VC"},{"label":"Samoa","value":"WS"},{"label":"San Marino","value":"SM"},{"label":"Sao Tome And Principe","value":"ST"},{"label":"Saudi Arabia","value":"SA"},{"label":"Senegal","value":"SN"},{"label":"Serbia","value":"RS"},{"label":"Seychelles","value":"SC"},{"label":"Sierra Leone","value":"SL"},{"label":"Singapore","value":"SG"},{"label":"Slovakia","value":"SK"},{"label":"Slovenia","value":"SI"},{"label":"Solomon Islands","value":"SB"},{"label":"Somalia","value":"SO"},{"label":"South Africa","value":"ZA"},{"label":"South Georgia And Sandwich Isl.","value":"GS"},{"label":"Spain","value":"ES"},{"label":"Sri Lanka","value":"LK"},{"label":"Sudan","value":"SD"},{"label":"Suriname","value":"SR"},{"label":"Svalbard And Jan Mayen","value":"SJ"},{"label":"Swaziland","value":"SZ"},{"label":"Sweden","value":"SE"},{"label":"Switzerland","value":"CH"},{"label":"Syrian Arab Republic","value":"SY"},{"label":"Taiwan","value":"TW"},{"label":"Tajikistan","value":"TJ"},{"label":"Tanzania","value":"TZ"},{"label":"Thailand","value":"TH"},{"label":"Timor-Leste","value":"TL"},{"label":"Togo","value":"TG"},{"label":"Tokelau","value":"TK"},{"label":"Tonga","value":"TO"},{"label":"Trinidad And Tobago","value":"TT"},{"label":"Tunisia","value":"TN"},{"label":"Turkey","value":"TR"},{"label":"Turkmenistan","value":"TM"},{"label":"Turks And Caicos Islands","value":"TC"},{"label":"Tuvalu","value":"TV"},{"label":"Uganda","value":"UG"},{"label":"Ukraine","value":"UA"},{"label":"United Arab Emirates","value":"AE"},{"label":"United Kingdom","value":"GB"},{"label":"United States","value":"US"},{"label":"United States Outlying Islands","value":"UM"},{"label":"Uruguay","value":"UY"},{"label":"Uzbekistan","value":"UZ"},{"label":"Vanuatu","value":"VU"},{"label":"Venezuela","value":"VE"},{"label":"Viet Nam","value":"VN"},{"label":"Virgin Islands, British","value":"VG"},{"label":"Virgin Islands, U.S.","value":"VI"},{"label":"Wallis And Futuna","value":"WF"},{"label":"Western Sahara","value":"EH"},{"label":"Yemen","value":"YE"},{"label":"Zambia","value":"ZM"},{"label":"Zimbabwe","value":"ZW"}]
        };
    },
    computed: {
        ...mapGetters({
            currentLocation: 'zipLookUp/getLocation',
            countries: 'shippingRates/countries',
        }),
        states: () => states,
    },
    methods: {
        ...mapActions({
            addAddress: 'customer/addAddress',
            updateAddress: 'customer/updateAddress',
        }),
        update(data) {
            if (data) {
                this.address = data;
                this.edit = true;
            }
            this.show = true;
        },
        reset() {
            this.address = Object.assign({}, a);
            this.edit = false;
            this.$v.$reset();
        },
        close() {
            this.reset();
            this.show = false;
        },
        Continue() {
            this.$v.address.$touch();
            if (!this.edit) {
                if (!this.$v.address.$invalid) {
                    this.$root.$refs.spinner.startSpinner();
                    this.addAddress(this.address)
                        .then((resp) => {
                            this.close();
                            this.$root.$refs.message.reportMsg(
                                this.createMsg,
                                'success'
                            );
                        })
                        .catch((err) => {
                            if (err.response.status == 422) {
                                this.errors = err.response.data.errors;
                            } else {
                                this.$root.$refs.spinner.stopSpinner();
                                handleError(err, this.$router);
                                this.$root.$refs.message.reportMsg(
                                    ErrorHandling.status(err.response.status),
                                    'error'
                                );
                            }
                        })
                        .then(() => this.$root.$refs.spinner.stopSpinner());
                }
            } else {
                if (!this.$v.address.$invalid) {
                    this.$root.$refs.spinner.startSpinner();
                    this.updateAddress(this.address)
                        .then((resp) => {
                            this.$root.$refs.message.reportMsg(
                                this.editMsg,
                                'success'
                            );
                            this.close();
                        })
                        .catch((err) => {
                            if (err.response.status == 422) {
                                this.errors = err.response.data.errors;
                            } else {
                                this.$root.$refs.spinner.stopSpinner();
                                handleError(err, this.$router);
                                this.$root.$refs.message.reportMsg(
                                    ErrorHandling.status(err.response.status),
                                    'error'
                                );
                            }
                        })
                        .then(() => this.$root.$refs.spinner.stopSpinner());
                }
            }
        },
    },
    watch: {
        show(val) {
            if (this.show) {
                this.address.country = this.currentLocation.country.value;
            }
        },
    },
    validations: {
        address: {
            name: { required },
            address_line_1: { required },
            city: { required },
            state: { required },
            postal_code: { required },
            country: { required },
        },
    },
};
</script>

<style scoped>
.error {
    border-color: #f66;
}
.small {
    min-height: 16px !important;
    display: flex;
    flex-direction: column;
}
.error-msg {
    padding-bottom: 8px;
    padding-top: 4px;
    color: #f66;
}
.wrepper {
    height: 100%;
    overflow: scroll;
}
.account_popup_inner {
    height: 100%;
    margin-top: 45px;
    margin-bottom: 30px;
}
</style>

<template>
    <div
        v-if="show"
        class="chart-container fadeIn faster animated"
        @click="close"
        id="chart-container"
    >
        <div class="chart-wrapper" id="chart-wrapper">
            <svg
                id="svg"
                @click="close"
                style="fill: black"
                class="ex"
                xmlns="http://www.w3.org/2000/svg"
                width="14.6"
                height="14.6"
                viewBox="0 0 23.635 23.634"
            >
                <defs></defs>
                <path
                    id="path"
                    class="a"
                    d="M49.982,28.114l-1.766-1.766L38.165,36.4,28.114,26.348l-1.766,1.766L36.4,38.165,26.348,48.216l1.766,1.766L38.165,39.931,48.216,49.982l1.766-1.766L39.931,38.165Z"
                    transform="translate(-26.348 -26.348)"
                />
            </svg>
            <div>
                <div id="actions">
                    <p class="confirm">
                        Are you sure you want to cancel the order?
                    </p>
                    <p id="yes" class="no_account_cont">Yes, Cancel Order</p>
                    <p id="no" class="back">No, Don't Cancel Order</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            show: false,
        };
    },
    methods: {
        open() {
            this.show = true;
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    this.$el.addEventListener('click', (e) => {
                        if (e.target.id == 'yes') {
                            resolve();
                            this.show = false;
                        }
                        if (
                            e.target.id == 'no' ||
                            e.target.id == 'chart-container' ||
                            e.target.id == 'svg' ||
                            e.target.id == 'path'
                        ) {
                            this.show = false;
                            reject();
                        }
                    });
                }, 500);
            });
        },
        close($event) {
            if (
                event.target.id == 'chart-container' ||
                event.target.id == 'svg' ||
                event.target.id == 'path'
            ) {
                this.show = false;
            }
        },
    },
};
</script>

<style scoped>
.chart-container {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.71);
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    overflow: auto;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
}
.chart-wrapper {
    border: 0.25px solid var(--gold_outline);
    max-width: 690px;
    max-height: 490px;
    min-width: 550.17px;
    /* min-height: 348px; */
    background-color: rgba(249, 246, 243, 1);
    overflow: auto;
    margin: auto;
    padding: 40px;
    position: relative;
}
.ex {
    /* width: 23px;
    height: 23px; */
    position: absolute;
    cursor: pointer;
    right: 17.87px;
    top: 18px;
}
.confirm {
    margin-bottom: 36px;
    font-size: 24px;
    text-align: center;
}
.back {
    text-align: center;
    font-size: 17px;
    cursor: pointer;
}
@media only screen and (max-width: 768px) {
    .chart-wrapper {
        max-width: 320px;
        max-height: 400px;
        min-width: 320px;
        /* min-height: 348px; */
    }
}
</style>

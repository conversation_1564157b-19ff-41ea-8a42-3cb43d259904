export default {
    computed: {
        grandTotal() {
            return (total, payments) => {
                let giftCards = _.get(payments, 'giftCard');
                if (giftCards && giftCards.length > 0) {
                    return (
                        total -
                        giftCards
                            .map((c) => c.amount)
                            .reduce((prev, current) => prev + current)
                    );
                } else {
                    return total;
                }
            };
        },

        paidWith() {
            return (order) => {
                let paymentInfo = _.get(order.payments, 'creditInfo');
                if (paymentInfo && paymentInfo.payment_type == 'creditCard') {
                    return `${_.capitalize(paymentInfo.type)} ... ${
                        paymentInfo.last_four
                    }`;
                } else if (
                    paymentInfo &&
                    paymentInfo.payment_type == 'payPal'
                ) {
                    return 'Paypal';
                }
            };
        },

        arrival_time() {
            return (order) => {
                const shipping = order.shipping;

                if (_.get(shipping, 'tracking.arrived_at')) {
                    return {
                        type:
                            _.get(shipping, 'tracking.status') ||
                            'Delivered' + ' on',
                        time: this.$options.filters.mmddyyyy(
                            shipping.tracking.arrived_at
                        ),
                    };
                } else if (_.get(shipping, 'tracking.arrival_date')) {
                    return {
                        type: 'Estimated Delivery',
                        time: this.$options.filters.mmddyyyy(
                            shipping.tracking.arrival_date
                        ),
                    };
                } else if (_.get(shipping, 'shippingType')) {
                    if (shipping.shippingType.delivery) {
                        return {
                            type: 'Estimated Delivery',
                            time: this.$options.filters.mmddyyyy(
                                shipping.shippingType.estimated_arrival
                            ),
                        };
                    } else {
                        if (order.status.toLowerCase() === 'ready') {
                            return { type: 'Ready for Pickup', time: '' };
                        }
                        return {
                            type: 'Ready for Pickup',
                            time: shipping.shippingType.date,
                        };
                    }
                } else {
                    return false;
                }
            };
        },

        hasOnlyDigitalItems() {
            return (products) => {
                let onlyDigital = true;
                products.forEach((p) => {
                    onlyDigital =
                        onlyDigital &&
                        (p.item_type == 'digital' || p.type == 'giftCard');
                });
                return onlyDigital;
            };
        },

        canCancel() {
            return (order) => {
                return (
                    !order.products.find(
                        (p) => _.get(p, 'status') == 'cancelled'
                    ) &&
                    order.status == 'paid' &&
                    !order.products.find((p) => _.get(p, 'personalization')) &&
                    !this.hasOnlyDigitalItems(order.products) &&
                    order.products.filter((p) => p.returns_left != 0).length > 0
                );
            };
        },

        canReturn() {
            return (date, product) => {
                let d = new Date(date);

                return (
                    d >= new Date() &&
                    _.get(product, 'returns_left') > 0 &&
                    _.get(product, 'status') != 'cancelled' &&
                    !_.get(product, 'personalization')
                );
            };
        },
    },

    methods: {
        metaString(meta) {
            meta = JSON.parse(meta);
            return _.map(meta, (val, key, index) => {
                return `${key}: ${val}`;
            }).join(' | ');
        },

        formatCity(city, state, zip) {
            return `${city}, ${state} ${zip}`;
        },

        hasGiftOptions(products) {
            return products
                .filter((p) => _.get(p, 'gift_options') != null)
                .map((p) => p.gift_options.price)
                .reduce((prev, curr) => prev + curr, 0);
        },

        hasPersonalization(products) {
            return products
                .filter((p) => _.get(p, 'personal') != null)
                .map((p) => p.personal.total)
                .reduce((prev, curr) => prev + curr, 0);
        },

        returnedString(returned) {
            if (returned.status) {
                return `${_.capitalize(returned.status)} return ${
                    returned.string == '1' ? '' : returned.string
                }`;
            }
        },
    },
};

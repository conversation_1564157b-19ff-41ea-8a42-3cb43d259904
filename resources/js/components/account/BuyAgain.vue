<template>
    <div v-if="orders.length > 0" class="user_content_wrapper page_buyagain">
        <div class="usc_center">
            <div class="usc_ttl_sec">
                <p class="usc_ttl">Buy it Again</p>
                <div class="usc_sort">
                    <span class="usc_sort_ttl">Sort by:</span>
                    <select @change="sortBy">
                        <option selected value="R">Most Recent Order</option>
                        <option value="O">Most Oldest Order</option>
                    </select>
                </div>
            </div>
            <div class="user_card">
                <div
                    class="uc_order_item"
                    v-for="(order, index) in orders"
                    :key="index"
                >
                    <router-link class="ucoi_thumb" :to="getProductLink(order)">
                        <img v-if="order.media" :src="order.media" />
                    </router-link>
                    <div class="ucoi_info">
                        <p v-if="order.vendor" class="ucoi_one">
                            {{ order.vendor }}
                        </p>
                        <router-link
                            class="ucoi_two"
                            :to="getProductLink(order)"
                            >{{ order.title }}</router-link
                        >
                        <div class="ucoi_three">
                            <p class="ucoi_price fav_price">$25.99</p>
                        </div>
                        <div class="ucoi_four buyagain_four">
                            <p>{{ metaString(order.meta) }}</p>
                        </div>
                        <p class="buyagain_past">
                            Ordered &times;{{ order.count }}
                        </p>
                    </div>
                    <div class="ucoi_actions">
                        <p
                            v-if="!added(order.id)"
                            class="uc_head_btn"
                            @click="addProduct(order)"
                        >
                            Add to Bag
                        </p>
                        <p
                            v-if="added(order.id)"
                            class="uc_head_btn pulse animated"
                            style="transition: none"
                        >
                            Added
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="no-products">
        <div>You haven’t ordered any items yet!</div>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import state from '../products/state';
import productLinkMixin from '../bag/productLinkMixin';
export default {
    mixins: [productLinkMixin],

    data() {
        return {
            orders: [],
            state,
        };
    },
    computed: {
        ...mapGetters(['products']),
    },
    methods: {
        ...mapActions(['addToCart']),
        requestOrders() {
            this.$root.$refs.spinner.startSpinner();
            axios('/api/customer/again')
                .then((resp) => {
                    this.$root.$refs.spinner.stopSpinner();
                    this.orders = resp.data;
                })
                .catch((error) => {
                    this.$root.$refs.spinner.stopSpinner();
                    handleError(error, this.$router);
                });
        },
        metaString(meta) {
            meta = JSON.parse(meta);
            return _.map(meta, (val, key, index) => {
                return `${key}: ${val}`;
            }).join(' | ');
        },
        addProduct(product) {
            this.state.setProduct(product);
            this.addToCart({ ...this.state.getCurrent(), ...{ quantity: 1 } });
        },
        added(id) {
            let index = this.products.findIndex((p) => p.id == id);
            return index != -1;
        },
        sortBy($event) {
            this.orders.reverse();
        },
    },
    mounted() {
        this.requestOrders();
    },
};
</script>

<style scoped>
.no-products {
    /* font-size: 16px;
    font-weight: 300; 
    display: flex;
    align-items: center;
    width: 100vw;
    justify-content: center;
	position: fixed; */
    font-size: 16px;
    font-weight: 300;
    /* display: flex;
    align-items: center;
    justify-content: center;
	margin: 40px; */
    margin-bottom: 40px;
    width: 100%;
}
.no-products div {
    margin-left: calc(50% - 208.52px);
}
</style>

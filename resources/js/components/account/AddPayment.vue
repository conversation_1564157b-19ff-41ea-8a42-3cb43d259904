<template>
    <transition
        enter-active-class="fadeIn faster animated"
        leave-active-class="fadeOut faster animated"
    >
        <div v-show="show" class="account_popup wrepper">
            <div class="account_popup_ex">
                <img @click="close()" src="/img/ex_grey.svg" />
            </div>
            <div class="account_popup_inner">
                <span v-if="!edit">
                    <p class="checkout_card_ttl">Add Payment Information</p>

                    <!-- Name -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Name on Card"
                            autocomplete="cc-name"
                            v-model.trim="$v.creditInfo.name.$model"
                            :class="{
                                error:
                                    $v.creditInfo.name.$invalid &&
                                    $v.creditInfo.name.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditInfo.name.$dirty &&
                                !$v.creditInfo.name.required
                            "
                            class="error-msg"
                            >Name is required</small
                        >
                    </div>

                    <!-- cardNumber -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            id="cNum"
                            type="tel"
                            name=""
                            class="pcl_input_text"
                            placeholder="Card Number"
                            autocomplete="cc-number"
                            v-model.trim="$v.ccNumber.$model"
                            :class="{
                                error:
                                    ($v.ccNumber.$invalid ||
                                        (showInvalidNumber && !validNumber)) &&
                                    $v.ccNumber.$dirty,
                            }"
                            :style="backroundImg"
                            @blur="showInvalidNumber = !validNumber"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="showInvalidNumber && !validNumber"
                            class="error-msg"
                            >Invalid Number</small
                        >
                        <small
                            v-show="$v.ccNumber.$dirty && !$v.ccNumber.required"
                            class="error-msg"
                            >Card number is required</small
                        >
                    </div>

                    <!-- exp & cvc -->
                    <div
                        class="checkout_half_wrap"
                        :style="{ 'margin-bottom': '-13px' }"
                    >
                        <div class="pcl_input_set">
                            <input
                                id="Exp"
                                type="tel"
                                name="cc-exp"
                                class="pcl_input_text"
                                placeholder="MM/YY"
                                autocomplete="cc-exp"
                                v-model="$v.creditInfo.expDate.$model"
                                :class="{
                                    error:
                                        ($v.creditInfo.expDate.$invalid ||
                                            (showInvalidExp && !validExp)) &&
                                        $v.creditInfo.expDate.$dirty,
                                }"
                                @blur="showInvalidExp = !validExp"
                            />
                        </div>
                        <div class="pcl_input_set">
                            <input
                                id="Cvc"
                                type="tel"
                                name=""
                                class="pcl_input_text"
                                placeholder="Security Code"
                                autocomplete="cc-csc"
                                v-model="$v.creditInfo.securityCode.$model"
                                :class="{
                                    error:
                                        ($v.creditInfo.securityCode.$invalid ||
                                            (showInvalidCvc && !validCvc)) &&
                                        $v.creditInfo.securityCode.$dirty,
                                }"
                                @blur="showInvalidCvc = !validCvc"
                            />
                        </div>
                    </div>
                    <div class="error-wrepper">
                        <div class="small">
                            <small
                                v-show="showInvalidExp && !validExp"
                                class="error-msg"
                                >Invalid Expiration Date</small
                            >
                            <small
                                v-show="
                                    $v.creditInfo.expDate.$dirty &&
                                    !$v.creditInfo.expDate.required
                                "
                                class="error-msg"
                                >Expiration Date is required</small
                            >
                        </div>
                        <div class="small">
                            <small
                                v-show="
                                    $v.creditInfo.securityCode.$dirty &&
                                    !$v.creditInfo.securityCode.required
                                "
                                class="error-msg"
                                >Security Code is required</small
                            >
                        </div>
                    </div>

                    <div class="checkout_cb_box ccb_offers">
                        <div class="ccb_left">
                            <input
                                type="checkbox"
                                name=""
                                class="checkout_cb"
                                id="ccb_1"
                                v-model.trim="creditInfo.default"
                            />
                            <label class="checkout_cb_vis" for="ccb_1"></label>
                            <label class="checkout_label" for="ccb_1"
                                >Set as preferred payment</label
                            >
                        </div>
                    </div>

                    <p class="checkout_card_ttl">Billing Information</p>
                    <!-- Street Address -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Street Address"
                            v-model.trim="$v.creditInfo.address_line_1.$model"
                            :class="{
                                error:
                                    $v.creditInfo.address_line_1.$invalid &&
                                    $v.creditInfo.address_line_1.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditInfo.address_line_1.$dirty &&
                                !$v.creditInfo.address_line_1.required
                            "
                            class="error-msg"
                            >Address is required</small
                        >
                    </div>

                    <!-- Apt., Floor, Unit etc. -->
                    <div class="pcl_input_set">
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Apt., Floor, Unit etc."
                            v-model.trim="creditInfo.address_line_2"
                        />
                    </div>

                    <!-- country -->
                    <div
                        class="bir_qty_box"
                        style="width: 100%; margin-bottom: 16px"
                        :class="{
                            error:
                                $v.creditInfo.country.$invalid &&
                                $v.creditInfo.country.$dirty,
                        }"
                    >
                        <select
                            v-model="$v.creditInfo.country.$model"
                            class="bir_qty"
                            style="font-size: 17px"
                        >
                            <option selected disabled value="">
                                Select your Country
                            </option>
                            <option
                                v-for="country in countries"
                                :key="country.value"
                                :value="country.value"
                            >
                                {{ country.label }}
                            </option>
                        </select>
                    </div>

                    <!-- State -->
                    <div
                        v-if="creditInfo.country == 'US'"
                        class="bir_qty_box"
                        style="width: 100%"
                        :class="{
                            error:
                                $v.creditInfo.state.$invalid &&
                                $v.creditInfo.state.$dirty,
                        }"
                    >
                        <select
                            v-model="$v.creditInfo.state.$model"
                            class="bir_qty"
                            style="font-size: 17px"
                            autocomplete="shipping region"
                            name="ship-state"
                        >
                            <option selected disabled value="">
                                Select your State
                            </option>
                            <option
                                v-for="(state, index) in states"
                                :key="index"
                                :value="state.abbreviation"
                            >
                                {{ state.name }}
                            </option>
                        </select>
                    </div>

                    <!-- State  -->
                    <div
                        v-else
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name="ship-state"
                            class="pcl_input_text"
                            placeholder="State/Province"
                            autocomplete="shipping region"
                            v-model.trim="$v.creditInfo.state.$model"
                            :class="{
                                error:
                                    $v.creditInfo.state.$invalid &&
                                    $v.creditInfo.state.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditInfo.state.$dirty &&
                                !$v.creditInfo.state.required
                            "
                            class="error-msg"
                            >State is required</small
                        >
                    </div>

                    <!-- City  -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="City"
                            v-model.trim="$v.creditInfo.city.$model"
                            :class="{
                                error:
                                    $v.creditInfo.city.$invalid &&
                                    $v.creditInfo.city.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditInfo.city.$dirty &&
                                !$v.creditInfo.city.required
                            "
                            class="error-msg"
                            >City is required</small
                        >
                    </div>

                    <!-- Zip Code -->
                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Zip Code"
                            v-model.trim="$v.creditInfo.postal_code.$model"
                            :class="{
                                error:
                                    $v.creditInfo.postal_code.$invalid &&
                                    $v.creditInfo.postal_code.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditInfo.postal_code.$dirty &&
                                !$v.creditInfo.postal_code.required
                            "
                            class="error-msg"
                            >Zip code is required</small
                        >
                    </div>
                </span>
                <span v-else>
                    <p class="checkout_card_ttl">Edit Payment Information</p>

                    <div
                        class="pcl_input_set"
                        :style="{ 'margin-bottom': '1px' }"
                    >
                        <input
                            type="text"
                            name=""
                            class="pcl_input_text"
                            placeholder="Name on Card"
                            v-model.trim="$v.creditEdit.name.$model"
                            :class="{
                                error:
                                    $v.creditEdit.name.$invalid &&
                                    $v.creditEdit.name.$dirty,
                            }"
                        />
                    </div>
                    <div class="small">
                        <small
                            v-show="
                                $v.creditEdit.name.$dirty &&
                                !$v.creditEdit.name.required
                            "
                            class="error-msg"
                            >Name is required</small
                        >
                    </div>
                    <div
                        class="checkout_half_wrap"
                        :style="{ 'margin-bottom': '-13px' }"
                    >
                        <div class="pcl_input_set">
                            <input
                                id="Exp"
                                type="tel"
                                name=""
                                class="pcl_input_text"
                                placeholder="MM/YY"
                                v-model="$v.creditEdit.expDate.$model"
                                :class="{
                                    error:
                                        ($v.creditEdit.expDate.$invalid ||
                                            (showInvalidExp && !validExp)) &&
                                        $v.creditEdit.expDate.$dirty,
                                }"
                                @blur="showInvalidExp = !validExp"
                            />
                        </div>
                        <div class="pcl_input_set">
                            <input
                                id="Cvc"
                                type="tel"
                                name=""
                                class="pcl_input_text"
                                placeholder="Security Code"
                                v-model="$v.creditEdit.securityCode.$model"
                                :class="{
                                    error:
                                        ($v.creditEdit.securityCode.$invalid ||
                                            (showInvalidCvc && !validCvc)) &&
                                        $v.creditEdit.securityCode.$dirty,
                                }"
                                @blur="showInvalidCvc = !validCvc"
                            />
                        </div>
                    </div>
                    <div class="error-wrepper">
                        <div class="small">
                            <small
                                v-show="showInvalidExp && !validExp"
                                class="error-msg"
                                >Invalid Expiration Date</small
                            >
                            <small
                                v-show="
                                    $v.creditEdit.expDate.$dirty &&
                                    !$v.creditEdit.expDate.required
                                "
                                class="error-msg"
                                >Expiration Date is required</small
                            >
                        </div>
                        <div class="small">
                            <small
                                v-show="
                                    $v.creditEdit.securityCode.$dirty &&
                                    !$v.creditEdit.securityCode.required
                                "
                                class="error-msg"
                                >Security Code is required</small
                            >
                        </div>
                    </div>

                    <div class="checkout_cb_box ccb_offers">
                        <div class="ccb_left">
                            <input
                                type="checkbox"
                                name=""
                                class="checkout_cb"
                                id="ccb_1"
                                v-model.trim="creditEdit.default"
                            />
                            <label class="checkout_cb_vis" for="ccb_1"></label>
                            <label class="checkout_label" for="ccb_1"
                                >Set as preferred payment</label
                            >
                        </div>
                    </div>
                </span>
                <p
                    style="margin-bottom: 15px"
                    class="no_account_cont"
                    @click="Continue()"
                >
                    Continue
                </p>
            </div>
        </div>
    </transition>
</template>

<script>
import { required, email, integer } from 'vuelidate/lib/validators';
import payform from 'payform';
import { mapActions } from 'vuex';
import ErrorHandling from './errorHandling';
import countries from '../checkout/countries';
import states from '../checkout/states';
const c = {
    name: '',
    last_four: '',
    token: '',
    expDate: '',
    securityCode: '',
    type: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    default: false,
};
export default {
    data() {
        return {
            createMsg: 'Your Payment was successful added!',
            editMsg: 'Your Payment was successful updated!',
            show: false,
            edit: false,
            ccNumber: '',
            creditInfo: {
                name: '',
                last_four: '',
                token: '',
                expDate: '',
                securityCode: '',
                type: '',
                address_line_1: '',
                address_line_2: '',
                city: '',
                state: '',
                country: '',
                postal_code: '',
                default: false,
            },
            creditEdit: {
                name: '',
                expDate: '',
                default: '',
                securityCode: '',
            },
            validNumber: false,
            validExp: false,
            validCvc: false,
            showInvalidNumber: false,
            showInvalidExp: false,
            showInvalidCvc: false,
            visaUrl: 'url(/img/visa.png)',
            mastercardUrl: 'url(/img/mastercard.png)',
            amaxUrl: 'url(/img/amex.png)',
            discoverUrl: 'url(/img/discover.png)',
            jcbUrl: 'url(/img/jcb.png)',
            imgUrl: '',
        };
    },
    methods: {
        ...mapActions({
            addPayment: 'customer/addPayment',
            requetCardToken: 'customer/requetToken',
        }),
        update() {
            this.show = true;
            this.initilize();
        },
        reset() {
            this.creditInfo = Object.assign({}, c);
            this.$v.$reset();
            this.ccNumber = '';
            this.showInvalidNumber = false;
            this.showInvalidExp = false;
            this.showInvalidCvc = false;
            this.imgUrl = '';
            this.edit = false;
        },
        close() {
            this.reset();
            this.show = false;
        },
        initilize() {
            let cardInput = this.$el.querySelector('#cNum');
            let expInput = this.$el.querySelector('#Exp');
            let cvcInput = this.$el.querySelector('#Cvc');

            payform.cardNumberInput(cardInput);
            payform.expiryInput(expInput);
            payform.cvcInput(cvcInput);
        },
        Continue() {
            this.$v.creditInfo.$touch();
            this.$v.ccNumber.$touch();
            if (!this.$v.creditInfo.$invalid && !this.$v.ccNumber.$invalid) {
                this.$root.$refs.spinner.startSpinner();

                this.requetCardToken({
                    card: this.ccNumber,
                    exp: this.creditInfo.expDate,
                })
                    .then((resp) => {
                        this.creditInfo.token = resp.token;
                        this.creditInfo.last_four = resp.last_four;
                    })
                    .then(() => {
                        this.addPayment(this.creditInfo)
                            .then((resp) => {
                                this.close();
                                this.$root.$refs.message.reportMsg(
                                    this.createMsg,
                                    'success'
                                );
                            })
                            .catch((err) => {
                                this.$root.$refs.spinner.stopSpinner();
                                handleError(err, this.$router);
                                this.$root.$refs.message.reportMsg(
                                    ErrorHandling.status(err.response.status),
                                    'error'
                                );
                            });
                    })
                    .then(() => this.$root.$refs.spinner.stopSpinner());
            }
        },
        getImgUrl(type) {
            switch (type) {
                case 'visa':
                    this.imgUrl = this.visaUrl;
                    break;
                case 'mastercard':
                    this.imgUrl = this.mastercardUrl;
                    break;
                case 'discover':
                    this.imgUrl = this.discoverUrl;
                    break;
                case 'amex':
                    this.imgUrl = this.amaxUrl;
                    break;
                case 'jcb':
                    this.imgUrl = this.jcbUrl;
                    break;
                default:
                    this.imgUrl = '';
            }
        },
    },
    computed: {
        backroundImg() {
            return {
                backgroundRepeat: 'no-repeat',
                backgroundPosition: '98% center',
                backgroundImage: this.imgUrl,
                backgroundSize: '30px',
            };
        },
        countries: () => countries,

        states: () => states,
    },
    validations: {
        creditInfo: {
            name: { required },
            expDate: { required },
            securityCode: { required },
            address_line_1: { required },
            city: { required },
            state: { required },
            postal_code: { required },
            country: { required },
        },
        ccNumber: { required },
        creditEdit: {
            name: { required },
            expDate: { required },
            default: { required },
            securityCode: { required },
        },
    },
    watch: {
        ccNumber(val) {
            this.creditInfo.type = payform.parseCardType(val);
            this.getImgUrl(this.creditInfo.type);
            this.validNumber = payform.validateCardNumber(val);
        },
        creditInfo: {
            handler: function (newVal) {
                let expiryObj = payform.parseCardExpiry(newVal.expDate);
                this.validExp = payform.validateCardExpiry(expiryObj);

                this.validCvc = payform.validateCardCVC(newVal.securityCode);
            },
            deep: true,
        },
        creditEdit: {
            handler: function (newVal) {
                let expiryObj = payform.parseCardExpiry(
                    this.creditEdit.expDate
                );
                this.validExp = payform.validateCardExpiry(expiryObj);
                this.validCvc = payform.validateCardCVC(
                    this.creditEdit.securityCode
                );
            },
            deep: true,
        },
    },
};
</script>

<style scoped>
.error {
    border-color: #f66;
}
.small {
    min-height: 16px !important;
    display: flex;
    flex-direction: column;
    width: calc(50% - 7.5px);
}
.error-msg {
    padding-bottom: 8px;
    padding-top: 4px;
    color: #f66;
}
.error-wrepper {
    display: flex;
    justify-content: space-between;
}
.wrepper {
    height: 100%;
    overflow: scroll;
}
.account_popup_inner {
    height: 100%;
    margin-top: 45px;
    margin-bottom: 30px;
}
</style>

export default {
    methods: {
        hasUrl(string) {
            const URLMatcher =
                /(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/gim;

            return string.replace(
                URLMatcher,
                (match) => `<a target="_blank" href="${match}">${match}</a>`
            );
        },
    },
};

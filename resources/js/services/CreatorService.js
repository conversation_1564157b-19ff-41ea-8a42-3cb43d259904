const CreatorService = {
    async getCreatorById(id) {
        try {
            const response = await axios.get(`/api/creators/${id}`);
            if (response.status === 200) {
                return response.data;
            } else {
                throw new Error('Failed to fetch creator data');
            }
        } catch (error) {
            console.error('Error fetching creator data:', error);
            throw error;
        }
    }
}

export default CreatorService;

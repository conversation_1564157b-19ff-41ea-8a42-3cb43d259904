import Vue from 'vue';
import Vuex from 'vuex';
import VuexPersist from 'vuex-persist';
import createMutationsSharer from 'vuex-shared-mutations';
import Bag from './bag';
import Checkout from './checkout';
import Breadcrumbs from './breadcrumbs';
import Customer from './customer';
import ZipLookUp from './zipLookUp';
import Following from './following';
import Discount from './discount';
import GiftCard from './giftCard';
import ShippingRates from './shippingRates';
import RecentlyViewed from './recentlyViewed';

Vue.use(Vuex);

const vuexPersist = new VuexPersist({
    key: 'eichlers',
});

export default new Vuex.Store({
    plugins: [
        vuexPersist.plugin,
        createMutationsSharer({
            predicate: (mutation, state) => {
                const predicate = [
                    'breadcrumbs/CREATE_BREADCRUMBS',
                    'breadcrumbs/CLEAR_BREADCRUMBS',
                ];

                return predicate.indexOf(mutation.type) == -1;
            },
        }),
    ],

    modules: {
        bag: Bag,
        checkout: Checkout,
        breadcrumbs: Breadcrumbs,
        customer: Customer,
        zipLookUp: ZipLookUp,
        following: Following,
        discount: Discount,
        giftCard: GiftCard,
        shippingRates: ShippingRates,
        recentlyViewed: RecentlyViewed,
    },
});

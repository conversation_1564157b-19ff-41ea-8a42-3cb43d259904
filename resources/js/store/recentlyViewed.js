export default {
    namespaced: true,
    state: {
        recentlyViewed: [],
    },
    mutations: {
        ADD_RECENT(state, payload) {
            state.recentlyViewed.splice(30);
            let index = state.recentlyViewed.findIndex(
                (r) => r.id == payload.id
            );
            if (index != -1) {
                state.recentlyViewed.splice(index, 1);
            }
            state.recentlyViewed.unshift(payload);
        },
    },
    getters: {
        recentlyViewed: (state) => state.recentlyViewed,
    },
    actions: {
        addRecenlty({ commit, state, rootGetters }, id) {
            let recent = { id: id, time_stamp: new Date() };
            if (rootGetters['customer/isAuth']) {
            } else {
                commit('ADD_RECENT', recent);
            }
        },
        sendRequest({}, payload) {},
    },
};

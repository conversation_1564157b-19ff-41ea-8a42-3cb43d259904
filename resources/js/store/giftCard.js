import Axios from 'axios';
import router from '../router';

let applyGiftCard = (total, totalApplied, amount) => {
    if (total > totalApplied) {
        return totalApplied + amount > total ? total - totalApplied : amount;
    } else {
        return 0;
    }
};

export default {
    namespaced: true,
    state: {
        giftcards: [],
        orderTotal: 0,
        totalApplied: 0,
    },
    mutations: {
        ADD_GIFT_CARD(state, card) {
            let index = state.giftcards.findIndex((c) => c.code == card.code);
            if (index == -1) {
                card['amount_applied'] = parseFloat(
                    applyGiftCard(
                        +state.orderTotal,
                        +state.totalApplied,
                        +card.balance
                    )
                );
                card['value_remaining'] = +card.balance - card.amount_applied;
                state.totalApplied += card.amount_applied;
                state.giftcards.push(card);
            }
        },
        REMOVE_GIFT_CARD(state, _card) {
            let index = state.giftcards.findIndex((c) => c.code == _card.code);
            if (index != -1) {
                state.totalApplied -= _card.amount_applied;
                state.giftcards.splice(index, 1);
            }
        },
        ADD_ORDER_TOTAL(state, total) {
            state.orderTotal = parseFloat(total);
        },
        CLEAR_STATE(state) {
            state.giftcards = [];
            state.orderTotal = 0;
            state.totalApplied = 0;
        },
    },
    getters: {
        giftCards: (state) => state.giftcards,

        totalGiftApplied: (state) => state.totalApplied,

        orderTotal: (state) => state.orderTotal,

        hasRemainingBal: (state) => state.orderTotal > state.totalApplied,
    },
    actions: {
        checkGiftCard({ commit }, card) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/gift_card', { code: card })
                    .then((resp) => {
                        commit('ADD_GIFT_CARD', {
                            code: card,
                            balance: resp.data,
                        });
                        resolve();
                    })
                    .catch((err) => {
                        if (_.get(err, 'response')) {
                            if (err.response.status == 400) {
                                reject(err.response.data.message);
                            } else {
                                handleError(err, router);
                            }
                        }
                    });
            });
        },
        updateGiftCards({ commit, state }) {
            let giftCards = state.giftcards.slice();
            state.giftcards = [];
            state.totalApplied = 0;
            giftCards.forEach((c) => {
                commit('ADD_GIFT_CARD', c);
            });
        },
        removeGiftCard({ commit, dispatch }, card) {
            commit('REMOVE_GIFT_CARD', card);
            dispatch('updateGiftCards');
        },
    },
};

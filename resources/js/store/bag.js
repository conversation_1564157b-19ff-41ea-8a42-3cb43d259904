import axios from 'axios';
import router from '../router';

const getDefaultState = () => {
    return {
        products: [],
        unavailableCart: [],
        unavailableLater: [],
        later: [],
        favorites: [],
        addOns: [],
        status: 'empty',
    };
};
export default {
    // namespace: true,
    state: getDefaultState(),

    mutations: {
        CLEAR_BAG_STATE(state) {
            Object.assign(state, getDefaultState());
        },
        ADD_TO_CART: (state, products) => {
            products.forEach((p) => {
                let index = state.products.findIndex(
                    (k) =>
                        k.id == p.id &&
                        k.type == p.type &&
                        k.item_key == p.item_key
                );
                if (index != -1) {
                    state.products.splice(index, 1);
                }
            });
            state.products.push(...products);
        },
        TOGGLE_ADD_ONS(state, addOn) {
            let index = state.addOns.findIndex((key) => {
                return (
                    Object.keys(key)[0] == Object.keys(addOn)[0] &&
                    Object.values(key)[0].id == Object.values(addOn)[0].id &&
                    Object.values(key)[0].type == Object.values(addOn)[0].type
                );
            });
            if (index == -1) {
                state.addOns.push(addOn);
            } else {
                state.addOns.splice(index, 1);
            }
        },
        // CLEAR_ADD_ONS(state,ids){
        //     let index = state.addOns.findIndex(p=> Object.keys(p)[0] == ids.productId && p[Object.keys(p)[0]].id == ids.addOnId)
        //     if(index != -1){
        //         state.addOns.splice(index,1);
        //     }
        // },
        CLEAR_ADD_ONS(state, id) {
            state.addOns.forEach((a) => {
                let index = state.addOns.findIndex(
                    (key) => Object.keys(key)[0] == id
                );
                state.addOns.splice(index, 1);
            });
        },
        ADD_UNAVAILABLE_TO_CART(state, unavailable) {
            state.unavailableCart = unavailable;
        },
        ADD_UNAVAILABLE_TO_LATER(state, unavailable) {
            state.unavailableLater = unavailable;
        },
        ADD_BULK_TO_CART(state, products) {
            state.products = products;
        },
        ADD_TO_LATER: (state, product) => {
            state.later.push(product);
        },
        ADD_BULK_TO_LATER(state, products) {
            state.later = products;
        },
        UPDATE_PRODUCT: (state, products) => {
            products.forEach((p) => {
                let index = state.products.findIndex(
                    (k) =>
                        k.id == p.id &&
                        k.type == p.type &&
                        k.item_key == p.item_key
                );
                if (index != -1) {
                    state.products[index].quantity = p.quantity;
                }
            });
        },
        UPDATE_LATER: (state, data) => {
            let index = state.later.findIndex(
                (later) =>
                    later.id == data.id &&
                    later.type == data.type &&
                    later.item_key == data.item_key
            );
            //state.products.splice(index, 1, _.merge(state.products[index], data))
            if (index != -1) {
                state.later[index].quantity = data.quantity;
            }
        },
        REMOVE_PRODUCT: (state, product) => {
            let indexCart = state.products.findIndex(
                (p) =>
                    p.id == product.id &&
                    p.type == product.type &&
                    p.item_key == product.item_key
            );
            if (indexCart != -1) {
                state.products.splice(indexCart, 1);
            }

            let indexUnavil = state.unavailableCart.findIndex(
                (p) =>
                    p.id == product.id &&
                    p.type == product.type &&
                    p.item_key == product.item_key
            );
            if (indexUnavil != -1) {
                state.unavailableCart.splice(indexUnavil, 1);
            }
        },
        REMOVE_LATER: (state, product) => {
            let indexLater = state.later.findIndex(
                (p) =>
                    p.id == product.id &&
                    p.type == product.type &&
                    p.item_key == product.item_key
            );
            if (indexLater != -1) {
                state.later.splice(indexLater, 1);
            }

            let indexUnavil = state.unavailableLater.findIndex(
                (p) =>
                    p.id == product.id &&
                    p.type == product.type &&
                    p.item_key == product.item_key
            );
            if (indexUnavil != -1) {
                state.unavailableLater.splice(indexUnavil, 1);
            }
        },
        ADD_FAVORITE(state, favorite) {
            state.favorites.push(favorite);
        },
        ADD_FAVORITES(state, favorites) {
            state.favorites = favorites;
        },
        REMOVE_FAVORITE(state, favorite) {
            let index = state.favorites.findIndex((f) => f == favorite);
            state.favorites.splice(index, 1);
        },
        CLEAR_CART(state) {
            state.products = [];
        },
        ADD_GIFT_OPTION(state, payload) {
            if (payload.status == 'current') {
                let index = state.products.findIndex((p) => p.id == payload.id);
                if (index != -1) {
                    state.products[index]['gift_options'] = payload.options;
                }
            } else {
                let index = state.later.findIndex((p) => p.id == payload.id);
                if (index != -1) {
                    state.later[index]['gift_options'] = payload.options;
                }
            }
        },
        DELETE_GIFT_OPTION(state, payload) {
            if (payload.status == 'current') {
                let index = state.products.findIndex(
                    (p) => p.id == payload.product.id
                );
                if (index != -1) {
                    delete state.products[index]['gift_options'];
                }
            } else {
                let index = state.later.findIndex(
                    (p) => p.id == payload.product.id
                );
                if (index != -1) {
                    delete state.later[index]['gift_options'];
                }
            }
        },
        REMOVE_PERSANALIZATION(state, payload) {
            if (payload.later) {
                state.later = payload.products;
            } else {
                state.products = payload.products;
            }
        },
        UPDATE_PERSONALIZATION(state, payload) {
            state.products = payload.products;
        },
    },

    getters: {
        addOns: (state) => state.addOns,

        favorites: (state) => state.favorites,

        products: (state) => state.products,

        unavailableCart: (state) => state.unavailableCart,

        unavailableLater: (state) => state.unavailableLater,

        hasOnlyDigitalItems(state) {
            if (state.products.length > 0) {
                let onlyDigital = true;
                state.products.forEach((p) => {
                    onlyDigital =
                        onlyDigital &&
                        (p.item_type == 'digital' ||
                            p.type == 'giftCard' ||
                            p.item_type == 'service');
                });
                return onlyDigital;
            }
            return false;
        },

        later: (state) => state.later,

        bagCount: (state) =>
            state.products
                .map((product) => {
                    return product.quantity;
                })
                .reduce((item, number) => +item + +number, 0),

        subTotal: (state) =>
            state.products
                .map((product) => {
                    return product.price * product.quantity;
                })
                .reduce((item, number) => item + number, 0),

        giftOptionsTotal: (state) => {
            return state.products
                .map((product) => {
                    return _.get(product, 'gift_options.price')
                        ? product.gift_options.price
                        : 0;
                })
                .reduce((prev, curr) => prev + curr, 0);
        },

        personalizationFee(state, getters) {
            let total = 0;
            getters.products.forEach((p) => {
                if (!p.personalization) return;

                for (let [key, value] of Object.entries(p.personalization)) {
                    total += p.personalization[key]
                        .map((k) => k.key.price)
                        .reduce((prev, curr) => prev + curr, 0);
                }
                total *= p.quantity;
            });
            return total;
        },

        taxTotal: (state, getters, rootState, rootGetters) =>
            state.products
                .map((product) => {
                    if (product.tax_code == 22 || product.type == 'giftCard') {
                        return 0;
                    }
                    return (
                        product.price *
                        product.quantity *
                        rootGetters['zipLookUp/getEstimatedTax']
                    );
                })
                .reduce((item, number) => +item + +number, 0),

        discountSavings: (state, getters, rootState, rootGetters) => {
            if (
                _.get(rootState.discount.discount, 'total') &&
                rootState.discount.discount.total > 0
            ) {
                return Number(rootState.discount.discount.total);
            }
            return 0;
        },

        grandTotal: (state, getters, rootState, rootGetters) => {
            if (getters.discountSavings) {
                let t =
                    getters.subTotal +
                    getters.taxTotal +
                    rootGetters['shippingRates/shippingPrice'] +
                    getters.giftOptionsTotal -
                    +getters.discountSavings;
                return t > 0 ? t : 0;
            }
            return (
                getters.subTotal +
                getters.taxTotal +
                rootGetters['shippingRates/shippingPrice'] +
                getters.giftOptionsTotal +
                getters.personalizationFee
            );
        },

        productById: (state, getters) => (id) =>
            _.find(state.products, { id: id }),
    },

    actions: {
        clearCart({ commit, getters, rootState }) {
            commit('CLEAR_CART');
            commit('discount/REMOVE_DISCOUNT', null, { root: true });
        },
        addFavorite({ commit, getters, rootState }, favorite) {
            return new Promise((resolve, reject) => {
                if (rootState.customer.isAuth) {
                    axios.post('/api/customer/favorites', { ids: favorite })
                        .then((resp) => {
                            commit('ADD_FAVORITES', resp.data);
                            resolve();
                        })
                        .catch((err) => reject(err));
                } else {
                    commit('ADD_FAVORITE', favorite);
                    resolve();
                }
            });
        },
        removeFavorite({ commit, getters, rootState }, favorite) {
            return new Promise((resolve, reject) => {
                let index = getters.favorites.findIndex((f) => favorite == f);
                if (index != -1) {
                    if (rootState.customer.isAuth) {
                        axios.patch('/api/customer/favorites', {
                            ids: favorite,
                        })
                            .then((resp) => {
                                commit('ADD_FAVORITES', resp.data);
                                resolve();
                            })
                            .catch((err) => reject(err));
                    } else {
                        commit('REMOVE_FAVORITE', favorite);
                        resolve();
                    }
                }
            });
        },
        processBag({ commit }, payload) {
            if (!payload.isLater) {
                commit(
                    'ADD_BULK_TO_CART',
                    adjustQuantity(payload.products.filter((p) => p.max > 0))
                );
                commit(
                    'ADD_UNAVAILABLE_TO_CART',
                    payload.products.filter((p) => p.max == 0)
                );
            } else {
                commit(
                    'ADD_BULK_TO_LATER',
                    adjustQuantity(payload.products.filter((p) => p.max > 0))
                );
                commit(
                    'ADD_UNAVAILABLE_TO_LATER',
                    payload.products.filter((p) => p.max == 0)
                );
            }
        },
        async sendCartToServer({ dispatch }, products) {
            await axios.post('/api/customer/bag', { bag: products })
                .then((resp) => {
                    dispatch('processBag', {
                        products: resp.data,
                        isLater: false,
                    });
                })
                .catch((err) => {});
        },
        async sendLaterToServer({ dispatch }, products) {
            await axios.post('/api/customer/bag', { later: products })
                .then((resp) => {
                    dispatch('processBag', {
                        products: resp.data,
                        isLater: true,
                    });
                })
                .catch((err) => {});
        },
        addToCart: ({ commit, getters, rootState, dispatch }, product) => {
            return new Promise((resolve, reject) => {
                let p = Array.isArray(product) ? product : [product];
                if (window.klaviyo) {
                    p.forEach((item) => {
                        window.klaviyo.track('Added to Cart', {
                            'ProductID': item.product_id,
                            'ProductName': item.title,
                            'ProductType': item.item_type,
                            'ProductSKU': item.sku,
                            'Quantity': item.quantity,
                            'Price': item.price,
                        });
                    });
                }
                if (!product.isUpdate) {
                    getters.products.forEach((k) => {
                        p.forEach((j) => {
                            if (
                                j.id == k.id &&
                                j.type == k.type &&
                                j.item_key == k.item_key
                            ) {
                                j.quantity += k.quantity;
                                j['gift_options'] = _.get(k, 'gift_options')
                                    ? k.gift_options
                                    : '';
                            }
                        });
                    });
                }
                if (rootState.customer.isAuth) {
                    axios.post('/api/customer/bag', { bag: p })
                        .then((resp) => {
                            dispatch('processBag', {
                                products: resp.data,
                                isLater: false,
                            });
                            resolve();
                        })
                        .catch((err) => {
                            reject(err);
                        });
                } else {
                    if (!product.isUpdate) {
                        commit('ADD_TO_CART', p);
                    } else {
                        commit('UPDATE_PRODUCT', p);
                    }
                    dispatch('checkAvailability', false);
                    resolve();
                }
            });
        },
        checkAvailability({ commit, getters, dispatch, rootState }, isLater) {
            let payload;

            if (isLater) {
                if (
                    getters.later.length > 0 ||
                    getters.unavailableLater.length > 0
                ) {
                    payload = [...getters.later, ...getters.unavailableLater];
                }
            } else {
                if (
                    getters.products.length > 0 ||
                    getters.unavailableCart.length > 0
                ) {
                    payload = [...getters.products, ...getters.unavailableCart];
                }
            }

            if (!payload) return;
            axios.post('/api/max', { products: payload })
                .then((resp) => {
                    if (!rootState.customer.isAuth) {
                        resp.data.forEach((p) => {
                            if (p.quantity > p.max) {
                                p.quantity = p.max;
                            } else if (p.quantity == 0 && p.max > 0) {
                                p.quantity = 1;
                            }
                        });
                    }
                    dispatch('processBag', {
                        products: resp.data,
                        isLater: isLater,
                    });
                })
                .catch((err) => {
                    handleError(err, router);
                });
        },
        async fetchCart({ dispatch }) {
            await axios('/api/customer/bag')
                .then((resp) => {
                    dispatch('processBag', {
                        products: resp.data,
                        isLater: false,
                    });
                })
                .catch((err) => {
                    handleError(err, router);
                });
        },
        async fetchLater({ dispatch }) {
            await axios('/api/customer/later')
                .then((resp) => {
                    dispatch('processBag', {
                        products: resp.data,
                        isLater: true,
                    });
                })
                .catch((err) => {
                    handleError(err, router);
                });
        },
        addToLater: ({ commit, getters, rootState, dispatch }, product) => {
            return new Promise((resolve, reject) => {
                if (rootState.customer.isAuth) {
                    let p = Array.isArray(product) ? product : [product];
                    axios.post('/api/customer/later', { later: p })
                        .then((resp) => {
                            let i = getters.products.findIndex(
                                (i) =>
                                    i.id == product.id &&
                                    i.type == product.type &&
                                    i.item_key == product.item_key
                            );
                            if (i != -1) {
                                dispatch('removeProduct', product);
                            }
                            dispatch('processBag', {
                                products: resp.data,
                                isLater: true,
                            });
                            resolve();
                        })
                        .catch((err) => {
                            reject(err);
                        });
                } else {
                    let old = _.find(getters.later, {
                        id: product.id,
                        item_key: product.item_key,
                    });
                    if (old) {
                        commit('UPDATE_LATER', product);
                        commit('REMOVE_PRODUCT', product);
                    } else {
                        commit('ADD_TO_LATER', product);
                        commit('REMOVE_PRODUCT', product);
                    }
                    dispatch('checkAvailability', true);
                    resolve();
                }
            });
        },
        removeProduct: ({ commit, getters, rootState, dispatch }, product) => {
            return new Promise((resolve, reject) => {
                if (rootState.customer.isAuth) {
                    let p = Array.isArray(product) ? product : [product];
                    axios.post('/api/customer/bag/delete', { bag: p })
                        .then((resp) => {
                            dispatch('processBag', {
                                products: resp.data,
                                isLater: false,
                            });
                            resolve();
                        })
                        .catch((err) => {
                            reject(err);
                        });
                } else {
                    if (Array.isArray(product)) {
                        commit('CLEAR_CART');
                    } else {
                        commit('REMOVE_PRODUCT', product);
                    }
                    resolve();
                }
            });
        },
        fromLater: ({ commit, getters, rootState, dispatch }, product) => {
            return new Promise((resolve, reject) => {
                if (rootState.customer.isAuth) {
                    dispatch('removeFromLater', product).then(() => {
                        dispatch('addToCart', product);
                        resolve();
                    });
                } else {
                    commit('REMOVE_LATER', product);
                    //commit('ADD_TO_CART', product);
                    dispatch('addToCart', product);
                    resolve();
                }
            });
        },
        removeFromLater: ({ commit, getters, rootState, dispatch }, later) => {
            return new Promise((resolve, reject) => {
                if (rootState.customer.isAuth) {
                    let p = Array.isArray(later) ? later : [later];
                    axios.post('/api/customer/later/delete', { later: p })
                        .then((resp) => {
                            dispatch('processBag', {
                                products: resp.data,
                                isLater: true,
                            });
                            resolve();
                        })
                        .catch((err) => {
                            reject(err);
                        });
                } else {
                    commit('REMOVE_LATER', later);
                    resolve();
                }
            });
        },
        addGiftOption({ commit, getters, rootState, dispatch }, payload) {
            commit('ADD_GIFT_OPTION', payload);
            if (rootState.customer.isAuth) {
                setTimeout(() => {
                    if (payload.status == 'current') {
                        dispatch('sendCartToServer', getters.products);
                    } else {
                        //dispatch("sendLaterToServer",getters.later)
                        dispatch('addToLater', getters.later);
                    }
                }, 100);
            }
        },
        removeGiftOption({ commit, getters, rootState, dispatch }, payload) {
            commit('DELETE_GIFT_OPTION', payload);
            if (rootState.customer.isAuth) {
                setTimeout(() => {
                    if (payload.status == 'current') {
                        dispatch('sendCartToServer', getters.products);
                    } else {
                        //dispatch("sendLaterToServer",getters.later)
                        dispatch('addToLater', getters.later);
                    }
                }, 100);
            }
        },
        removePersanalization(
            { commit, getters, rootState, dispatch, state },
            payload
        ) {
            let products = [];
            let later = [];

            if (payload.status == 'current') {
                let index = state.products.findIndex(
                    (p) => p.item_key == payload.item_key
                );
                if (index != -1) {
                    let prodIndex = state.products.findIndex((p) => {
                        return (
                            p.id == state.products[index].id &&
                            p.type == state.products[index].type &&
                            p.item_key == null
                        );
                    });

                    let cloned = Object.assign({}, state.products[index]);
                    if (rootState.customer.isAuth) {
                        cloned['personalization'] = null;
                        cloned['item_key'] = null;
                        state.products[index]['quantity'] = 0;
                    } else {
                        state.products[index]['personalization'] = null;
                        state.products[index]['item_key'] = null;
                    }

                    products = state.products.slice();
                    if (prodIndex != -1) {
                        let max = state.products[prodIndex].max;
                        let quantity = state.products[prodIndex].quantity;

                        state.products[prodIndex].quantity =
                            quantity + cloned.quantity > max
                                ? max
                                : quantity + cloned.quantity;
                        products = state.products.slice();
                        state.products.splice(index, 1);
                    } else {
                        products.push(cloned);
                    }

                    //needed since you can't mutate the state in an action
                    commit('REMOVE_PERSANALIZATION', {
                        products: state.products,
                        later: false,
                    });
                }
            } else {
                let index = state.later.findIndex(
                    (p) => p.item_key == payload.item_key
                );
                if (index != -1) {
                    let laterIndex = state.later.findIndex((p) => {
                        return (
                            p.id == state.later[index].id &&
                            p.type == state.later[index].type &&
                            p.item_key != state.later[index].item_key
                        );
                    });

                    let cloned = Object.assign({}, state.later[index]);
                    if (rootState.customer.isAuth) {
                        cloned['personalization'] = null;
                        cloned['item_key'] = null;

                        state.later[index]['quantity'] = 0;
                    } else {
                        state.later[index]['personalization'] = null;
                        state.later[index]['item_key'] = null;
                    }

                    later = state.later.slice();
                    if (laterIndex != -1) {
                        let max = state.later[laterIndex].max;
                        let quantity = state.later[laterIndex].quantity;

                        state.later[laterIndex].quantity =
                            quantity + cloned.quantity > max
                                ? max
                                : quantity + cloned.quantity;
                        later = state.later.slice();
                        state.later.splice(index, 1);
                    } else {
                        later.push(cloned);
                    }

                    //needed since you can't mutate the state in an action
                    commit('REMOVE_PERSANALIZATION', {
                        products: state.later,
                        later: true,
                    });
                }
            }
            if (rootState.customer.isAuth) {
                setTimeout(() => {
                    if (payload.status == 'current') {
                        dispatch('sendCartToServer', products);
                    } else {
                        dispatch('sendLaterToServer', later);
                    }
                }, 300);
            }
        },
        updatePersonalization(
            { commit, getters, rootState, dispatch, state },
            payload
        ) {
            const products = state.products;
            let index = products.findIndex(
                (p) => p.item_key == payload.item_key
            );
            if (index != -1) {
                products[index].personalization = payload.personalization;
                products[index].item_key = payload.item_key;
                commit('UPDATE_PERSONALIZATION', { products: products });
                if (rootState.customer.isAuth) {
                    setTimeout(() => {
                        dispatch('sendCartToServer', products);
                    }, 300);
                }
            }
        }
    },
};

function adjustQuantity(products) {
    let prod = products.slice();
    prod.forEach((p) => {
        p.quantity = p.quantity >= p.max ? p.max : p.quantity;
    });
    return prod;
}

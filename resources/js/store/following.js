import Axios from 'axios';

export default {
    namespaced: true,

    state: {
        creators: [],
        vendors: [],
    },
    mutations: {
        UPDATE_CREATORS(state, creators) {
            state.creators = creators;
        },
        UPDATE_VENDORS(state, vendors) {
            state.vendors = vendors;
        },
        CLEAR_ALL(state) {
            state.creators = [];
            state.vendors = [];
        },
    },
    getters: {
        followingCreators: (state) => state.creators,

        followingVendors: (state) => state.vendors,
    },
    actions: {
        follow({ state, commit, getters, rootGetters }, payload) {
            let index = state[payload.type + 's'].findIndex(
                (f) => f == payload.id
            );
            if (index == -1) {
                Axios.post('/api/customer/following', { following: payload })
                    .then((resp) => {
                        if (Object(resp.data)['creators']) {
                            commit(
                                'UPDATE_CREATORS',
                                Object(resp.data)['creators']
                            );
                        }
                        if (Object(resp.data)['vendors']) {
                            commit(
                                'UPDATE_VENDORS',
                                Object(resp.data)['vendors']
                            );
                        }
                    })
                    .catch((err) => {});
            } else {
                Axios.patch('/api/customer/following', { following: payload })
                    .then((resp) => {
                        if (Object(resp.data)['creators']) {
                            commit(
                                'UPDATE_CREATORS',
                                Object(resp.data)['creators']
                            );
                        } else {
                            commit('UPDATE_CREATORS', []);
                        }
                        if (Object(resp.data)['vendors']) {
                            commit(
                                'UPDATE_VENDORS',
                                Object(resp.data)['vendors']
                            );
                        } else {
                            commit('UPDATE_VENDORS', []);
                        }
                    })
                    .catch((err) => {});
            }
        },
        fetch({ commit }) {
            Axios('/api/customer/following')
                .then((resp) => {
                    if (_.get(Object(resp.data), 'creators')) {
                        commit(
                            'UPDATE_CREATORS',
                            Object(resp.data)['creators']
                        );
                    }
                    if (_.get(Object(resp.data), 'vendors')) {
                        commit('UPDATE_VENDORS', Object(resp.data)['vendors']);
                    }
                })
                .catch((err) => {});
        },
    },
};

const getDefaultState = () => {
    return {
        shippingType: null,
        shippingInfo: null,
        creditInfo: null,
        digitalInfo: null,
        pickupInfo: null,
        guestEmailExists: false,
        status: 'empty',
    };
};
export default {
    namespaced: true,
    state: getDefaultState(),

    mutations: {
        GUEST_EMAIL_EXISTS(state, val) {
            state.guestEmailExists = val;
        },
        CLEAR_STATE(state) {
            Object.assign(state, getDefaultState());
        },
        ADD_SHIPPING_TYPE(state, shipTyp) {
            state.shippingType = shipTyp;
        },
        ADD_SHIPPING_INFO(state, shipInfo) {
            state.shippingInfo = shipInfo;
        },
        ADD_DIGITAL_INFO(state, info) {
            state.digitalInfo = info;
        },
        ADD_PICKUP_INFO(state, info) {
            state.pickupInfo = info;
        },
        ADD_CREDIT_INFO(state, creditInfo) {
            state.creditInfo = creditInfo;
        },
        DELETE_SHIPPING_INFO(state) {
            state.shippingInfo = {};
        },
        DELETE_CREDIT_INFO(state) {
            state.creditInfo = {};
        },
    },
    getters: {
        guestEmailExists(state) {
            return state.guestEmailExists;
        },
        _shippingType(state) {
            return state.shippingType;
        },
        _shippingInfo(state, getters, rootState, rootGetters) {
            if (rootGetters['customer/isAuth']) {
                if (
                    _.get(rootGetters['customer/getCustomer'], 'addresses') &&
                    rootGetters['customer/getCustomer'].addresses.length > 0
                ) {
                    if (!_.isEmpty(state.shippingInfo)) {
                        return state.shippingInfo;
                    }
                    let default_ = rootGetters[
                        'customer/getCustomer'
                    ].addresses.findIndex((a) => a.default == true);
                    return default_ == -1
                        ? rootGetters['customer/getCustomer'].addresses[0]
                        : rootGetters['customer/getCustomer'].addresses[
                              default_
                          ];
                }
            } else {
                return state.shippingInfo;
            }
        },
        _creditInfo(state, getters, rootState, rootGetters) {
            if (rootGetters['customer/isAuth']) {
                if (
                    _.get(rootGetters['customer/getCustomer'], 'payments') &&
                    rootGetters['customer/getCustomer'].payments.length > 0
                ) {
                    if (!_.isEmpty(state.creditInfo)) {
                        return state.creditInfo;
                    }
                    let _default = rootGetters[
                        'customer/getCustomer'
                    ].payments.findIndex((a) => a.default == true);
                    return _default == -1
                        ? rootGetters['customer/getCustomer'].payments[0]
                        : rootGetters['customer/getCustomer'].payments[
                              _default
                          ];
                }
            } else {
                return state.creditInfo;
            }
        },
        digitalInfo(state) {
            return state.digitalInfo;
        },
        pickupInfo(state, getters, rootState, rootGetters) {
            if (state.pickupInfo) return state.pickupInfo;
            else if (rootGetters['customer/isAuth']) {
                const customer = rootGetters['customer/getCustomer'];
                if (customer.addresses && customer.addresses.length > 0) {
                    let address = customer.addresses.find((a) => a.default);
                    address == undefined
                        ? (address = customer.addresses[0])
                        : '';
                    return {
                        name: customer.name,
                        email: customer.email,
                        phone: address.phone,
                    };
                }
            }
        },
        isPickup(state) {
            if (!state.shippingType) return false;
            return !state.shippingType.delivery;
        },
    },
    actions: {},
};

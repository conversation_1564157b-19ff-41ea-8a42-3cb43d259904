import Axios from 'axios';
import router from '../router/index.js';
import Bugsnag from '@bugsnag/js';
import { safeIdentify, identifyAuthenticatedUser } from '../utils/klaviyo';
export default {
    namespaced: true,

    state: {
        isAuth: false,
        customer: {},
    },
    mutations: {
        INITIALIZE_CUSTOMER(state, customer) {
            state.isAuth = true;
            state.customer = customer;
        },
        DESTROW_CUSTOMER(state) {
            state.isAuth = false;
            state.customer = {};
        },
        ADD_ADDRESS(state, addresses) {
            // state.customer.addresses.push(address);
            state.customer.addresses = addresses;
        },
        UPDATE_ADDRESS(state, address) {
            let index = state.customer.addresses.findIndex(
                (e) => e.id == address.id
            );
            if (index != -1) {
                state.customer.addresses[index] = address;
            }
        },
        DELETE_ADDRESS(state, address) {
            let index = state.customer.addresses.findIndex(
                (e) => e.id == address.id
            );
            if (index != -1) {
                state.customer.addresses.splice(index, 1);
            }
        },
        ADD_PAYMENT(state, payments) {
            state.customer.payments = payments;
        },
        UPDATE_PAYMENT(state, payment) {
            let index = state.customer.payments.findIndex(
                (e) => e.id == payment.id
            );
            if (index != -1) {
                state.customer.payments[index] = payment;
            }
        },
        DELETE_PAYMENT(state, payment) {
            let index = state.customer.payments.findIndex(
                (e) => e.id == payment.id
            );
            if (index != -1) {
                state.customer.payments.splice(index, 1);
            }
        },
        UPDATE_CUSTOMER(state, data) {
            state.customer = data;
        },
    },
    getters: {
        getFirstName(state) {
            if (state.customer && state.customer.name) {
                state.customer.name.trim();
                let index = state.customer.name.indexOf(' ');
                return index == -1
                    ? state.customer.name
                    : state.customer.name.substring(0, index);
            }
        },
        getCustomer(state) {
            return state.customer;
        },
        isAuth(state) {
            return state.isAuth;
        },
    },
    actions: {
        login({ commit, rootGetters, dispatch }, cred) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/login', cred)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('giftCard/CLEAR_STATE', null, {
                                root: true,
                            });
                            commit('INITIALIZE_CUSTOMER', resp.data);
                            commit('zipLookUp/RESET_LOCATION', null, {
                                root: true,
                            });
                            dispatch('following/fetch', null, { root: true });
                            commit('checkout/CLEAR_STATE', null, {
                                root: true,
                            });
                            if (rootGetters.favorites.length > 0) {
                                dispatch('addFavorite', rootGetters.favorites, {
                                    root: true,
                                });
                            } else {
                                if (resp.data.favorites) {
                                    commit(
                                        'ADD_FAVORITES',
                                        resp.data.favorites,
                                        { root: true }
                                    );
                                }
                            }
                            if (rootGetters.later.length > 0) {
                                //dispatch("sendLaterToServer",rootGetters.later,{root:true})
                                dispatch('addToLater', rootGetters.later, {
                                    root: true,
                                });
                            } else {
                                dispatch('fetchLater', null, { root: true });
                            }
                            if (rootGetters.products.length > 0) {
                                dispatch(
                                    'sendCartToServer',
                                    rootGetters.products,
                                    {
                                        root: true,
                                    }
                                ).then(() => resolve());
                            } else {
                                dispatch('fetchCart', null, {
                                    root: true,
                                }).then(() => resolve());
                            }

                            const customer = resp.data;
                            Bugsnag.setUser(
                                customer.id,
                                customer.email,
                                customer.name
                            );

                            // Identify customer in Klaviyo
                            safeIdentify(identifyAuthenticatedUser, customer);

                            //resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        createCustomerAtOrder({ commit, rootGetters, dispatch }, user) {
            commit('INITIALIZE_CUSTOMER', user);

            // Identify customer in Klaviyo
            safeIdentify(identifyAuthenticatedUser, user);

            if (rootGetters.favorites.length > 0) {
                dispatch('addFavorite', rootGetters.favorites, { root: true });
            }
            if (rootGetters.later.length > 0) {
                dispatch('sendLaterToServer', rootGetters.later, {
                    root: true,
                });
            }
        },
        createCustomer({ commit, rootGetters, dispatch }, user) {
            commit('INITIALIZE_CUSTOMER', user);

            // Identify customer in Klaviyo
            safeIdentify(identifyAuthenticatedUser, user);

            if (rootGetters.favorites.length > 0) {
                dispatch('addFavorite', rootGetters.favorites, { root: true });
            }
            if (rootGetters.later.length > 0) {
                dispatch('sendLaterToServer', rootGetters.later, {
                    root: true,
                });
            }
            if (rootGetters.products.length > 0) {
                dispatch('sendCartToServer', rootGetters.products, {
                    root: true,
                });
            }
            if (rootGetters.later.length > 0) {
                dispatch('sendLaterToServer', rootGetters.later, {
                    root: true,
                });
            }
        },
        logout({ state, commit, rootState, dispatch }) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/logout')
                    .then((resp) => {
                        dispatch('deleteCustomerInfo');
                        resolve();
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        deleteCustomerInfo({ commit }) {
            commit('DESTROW_CUSTOMER');
            commit('CLEAR_BAG_STATE', null, { root: true });
            commit('checkout/CLEAR_STATE', null, { root: true });
            commit('zipLookUp/RESET_LOCATION', null, { root: true });
            commit('following/CLEAR_ALL', null, { root: true });
            commit('discount/REMOVE_DISCOUNT', null, { root: true });
            commit('giftCard/CLEAR_STATE', null, { root: true });
        },
        isLoggedIn({ dispatch, getters }) {
            if (getters.isAuth) {
                Axios('/api/customer')
                    .then((resp) => {
                        // Authentication confirmed - identify customer in Klaviyo
                        const customer = getters.getCustomer;
                        if (customer && customer.email) {
                            safeIdentify(identifyAuthenticatedUser, customer);
                        }
                    })
                    .catch((err) => {
                        if (err.response.status == 403) {
                            dispatch('deleteCustomerInfo');
                            router.push({ name: 'Home' }).catch((err) => {});
                        }
                    });
            }
        },
        resetPassword({ commit, rootGetters, dispatch }, cred) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/password/reset', cred)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('INITIALIZE_CUSTOMER', resp.data);
                            if (rootGetters.favorites.length > 0) {
                                dispatch('addFavorite', rootGetters.favorites, {
                                    root: true,
                                });
                            } else {
                                if (resp.data.favorites) {
                                    commit(
                                        'ADD_FAVORITES',
                                        resp.data.favorites,
                                        { root: true }
                                    );
                                }
                            }
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        updateCustomer({ commit }, data) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/customer', data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('UPDATE_CUSTOMER', resp.data);
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        addAddress({ commit }, data) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/customer/address', data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('ADD_ADDRESS', resp.data.addresses);
                            resolve(resp.data.address);
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        updateAddress({ commit, dispatch, state }, data) {
            return new Promise((resolve, reject) => {
                Axios.post(`/api/customer/address/${data.id}`, data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('ADD_ADDRESS', resp.data.addresses);
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        deleteAddress({ commit }, data) {
            return new Promise((resolve, reject) => {
                Axios.delete(`/api/customer/address/${data.id}`, data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('DELETE_ADDRESS', resp.data.address);
                            commit('checkout/DELETE_SHIPPING_INFO', null, {
                                root: true,
                            });
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        addPayment({ commit, dispatch }, data) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/customer/payment', data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('ADD_PAYMENT', resp.data.payments);
                            resolve(resp.data.payment);
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        updatePayment({ commit }, data) {
            return new Promise((resolve, reject) => {
                //dispatch("submitCardInfo",data).then(res=>{
                Axios.post(`/api/customer/payment/${data.id}`, data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('ADD_PAYMENT', resp.data.payments);
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
            //})
        },
        deletePayment({ commit }, data) {
            return new Promise((resolve, reject) => {
                Axios.delete(`/api/customer/payment/${data.id}`, data)
                    .then((resp) => {
                        if (resp.status == 200) {
                            commit('DELETE_PAYMENT', data);
                            commit('checkout/DELETE_CREDIT_INFO', null, {
                                root: true,
                            });
                            resolve();
                        }
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        requetToken({ commit }, payload) {
            return new Promise((resolve, reject) => {
                Axios.post('/api/card/createToken', {
                    card: payload.card.replace(/\s/g, ''),
                    exp: payload.exp.replace(/\s/g, '').replace('/', ''),
                })
                    .then((resp) => {
                        resolve({
                            token: resp.data.Token,
                            last_four: payload.card.slice(
                                payload.card.length - 4
                            ),
                        });
                    })
                    .catch((err) => {
                        reject(err.response);
                    });
            });
        },
    },
};

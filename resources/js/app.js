import './bootstrap';
import Vue from 'vue';

window.Vue = Vue;
Vue.prototype._ = window._;

// Application styles
import '../../public/css/style.css';
import '../../public/css/components.css';
import '../../public/css/tablet.css';
import '../../public/css/mobile.css';

// Library styles
import 'simple-keyboard/build/css/index.css';
import 'animate.css/animate.min.css';

import Vuelidate from 'vuelidate';
import Router from 'vue-router';
import Vuex from 'vuex';

Vue.use(Router);
Vue.use(Vuex);
Vue.use(Vuelidate);

import * as Helpers from './mixins/index.js';

Helpers.registerFilters(Vue);
Helpers.processPolyfills();

// convert css variables for E11
Helpers.processCssVars();

// Google Analytics
window.dataLayer = window.dataLayer || [];
Helpers.gtag('js', new Date());
Helpers.gtag('config', 'G-TB5RJ3SMDC');

//Google Ads
Helpers.gtag('config', 'AW-668867093', { allow_enhanced_conversions: true });

import { register } from './mixins/registry';
register(Vue);

import { registerDirectives } from './mixins/directives';
registerDirectives(Vue);

import { handleError } from './mixins/handleErrors.js';
window.handleError = handleError;

import { isRedirect } from './mixins/redirectHandler.js';
window.isRedirect = isRedirect;

import router from './router/index.js';
import store from './store/index.js';

// import Bugsnag from '@bugsnag/js';
// import BugsnagPluginVue from '@bugsnag/plugin-vue';

// if (process.env.NODE_ENV == 'production') {
//     Bugsnag.start({
//         apiKey: 'c7e442274488ecc6a1466576daea0be0',
//         appVersion: '2.0.0',
//         plugins: [new BugsnagPluginVue()],
//         //otherOptions: value
//     });
//     Bugsnag.getPlugin('vue').installVueErrorHandler(Vue);
// }

import AcceptProxyWarning from './components/partials/AcceptProxyWarning';
const proxyWarning = (Vue.prototype.$proxyWarning = new Vue(
    AcceptProxyWarning
).$mount());
document.body.appendChild(proxyWarning.$el);

window.Eichlers = new Vue({
    el: '#eichlers',
    router,
    store,

    provide() {
        return {
            keyboardEvents: new Vue(),
            componentsComunaction: new Vue(),
        };
    },

    mounted() {
        this.$store.dispatch('customer/isLoggedIn');
        this.$store.dispatch('shippingRates/getCountries');
        this.$store.dispatch('shippingRates/getCurrencies');
    },
});

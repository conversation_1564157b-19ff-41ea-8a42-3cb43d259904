import store from '../store';

const initialTitle = ' | Eichlers  ';
export default [
    {
        path: '/checkout',
        name: 'Checkout',
        component: () => import('../components/checkout/Checkout'),
        beforeEnter(to, from, next) {
            if (store.getters['products'].length > 0) {
                if (!store.getters['customer/isAuth']) {
                    if (from.name === 'SignIn') {
                        next();
                    } else if (from.name === 'Review') {
                        next();
                    } else {
                        next({ name: 'SignIn' });
                    }
                } else {
                    next({ name: 'Review' });
                }
            } else {
                next({ name: 'Bag' });
            }
        },
        meta: { title: 'Checkout' + initialTitle },
    },
    {
        path: '/review',
        name: 'Review',
        component: () => import('../components/checkout/ReviewOrder'),
        beforeEnter(to, from, next) {
            if (store.getters['products'].length > 0) {
                if (
                    !store.getters['customer/isAuth'] &&
                    from.name == 'Checkout'
                ) {
                    if (store.getters['hasOnlyDigitalItems']) {
                        if (store.getters['checkout/digitalInfo'] != null) {
                            next();
                        } else {
                            next({ name: 'Checkout' });
                        }
                    } else if (store.getters['checkout/isPickup']) {
                        if (store.getters['checkout/pickupInfo'] != null) {
                            next();
                        } else {
                            next({ name: 'Checkout' });
                        }
                    } else {
                        if (
                            store.getters['checkout/_shippingType'] != null &&
                            store.getters['checkout/_shippingInfo'] != null &&
                            (store.getters['checkout/_creditInfo'] != null ||
                                (store.getters['giftCard/giftCards'].length >
                                    0 &&
                                    !store.getters['giftCard/hasRemainingBal']))
                        ) {
                            next();
                        } else {
                            next({ name: 'Checkout' });
                        }
                    }
                } else {
                    next();
                }
            } else {
                next({ name: 'Bag' });
            }
        },
        meta: { title: 'Review' + initialTitle },
    },
    {
        path: '/thank-you/:id',
        name: 'ThankYou',
        component: () => import('../components/checkout/ThankYou'),
        meta: { title: 'Thank You' + initialTitle },
    },
    {
        path: '/view-invoice/:order_id',
        name: 'InvoiceViewer',
        component: () => import('../components/checkout/InvoiceViewer'),
        meta: { title: 'View Invoice' + initialTitle },
    },
    {
        path: '/orders/guest/:order_id/status',
        name: 'OrderStatus',
        component: () => import('../components/account/OrderStatus'),
        meta: { title: 'Order Status' + initialTitle },
    },
    {
        path: '/sign-in',
        name: 'SignIn',
        component: () => import('../components/checkout/SignIn'),
        beforeEnter(to, from, next) {
            if (
                !store.getters['customer/isAuth'] &&
                store.getters['products'].length > 0
            ) {
                next();
            } else {
                next(from.path);
            }
        },
        meta: { title: 'Sign In' + initialTitle },
    },
];

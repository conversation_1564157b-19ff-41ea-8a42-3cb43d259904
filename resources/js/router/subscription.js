import store from '../store';
const initialTitle = ' | Eichlers  ';

export default [
    {
        path: '/subscription/:type_id/:group_id',
        name: 'SubscriptionIndex',
        component: () =>
            import('../components/subscription/checkout/SubscriptionIndex'),
        meta: { title: 'Subscribe' + initialTitle },
        beforeEnter(to, from, next) {
            if (!store.getters['customer/isAuth']) {
                localStorage.setItem('prevUrl', to.path);
                next({ name: 'Login' });
            } else {
                next();
            }
        },
    },
    {
        path: '/subscription-confirmation/:subscription_id',
        name: 'SubscriptionConfirmation',
        component: () =>
            import(
                '../components/subscription/checkout/SubscriptionConfirmation'
            ),
        meta: { title: 'Subscription Confirmation' + initialTitle },
        props: true,
    },
    {
        path: '/recurring-confirmation/:recurring_id',
        name: 'RecurringConfirmation',
        component: () =>
            import('../components/recurring/checkout/RecurringConfirmation'),
        meta: { title: 'Subscription Confirmation' + initialTitle },
        props: true,
    },
];

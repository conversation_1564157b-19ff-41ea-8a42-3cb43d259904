import store from '../store';
import Router from 'vue-router';
import Checkout from './checkout';
import Account from './account';
import SchoolSupplies from './schoolSupplies';
import Subscriptions from './subscription';
import StoreInformation from './storeInfomation';
import Returns from './returns';

import { checkForUtm } from '../mixins/utmHelper';

const initialTitle = ' | Eichlers  ';

const router = new Router({
    mode: 'history',
    scrollBehavior(to, from, savedPosition) {
        if (to.hash) {
            return $('html,body')
                .stop()
                .animate({ scrollTop: $(to.hash).offset().top }, 500);
        }

        if (savedPosition) {
            return new Promise((resolve, reject) => {
                let timeout =
                    to.name == 'SearchIndex' || to.name == 'ProductsIndex'
                        ? 2000
                        : 500;

                setTimeout(() => {
                    resolve(savedPosition);
                }, timeout);
            });
        } else {
            return { x: 0, y: 0 };
        }
    },
    routes: [
        ...Checkout,
        ...Account,
        ...SchoolSupplies,
        ...Subscriptions,
        ...StoreInformation,
        ...Returns,
        {
            path: '/',
            name: 'Home',
            component: () => import('../components/home/<USER>'),
            meta: { title: 'Home' + initialTitle },
        },
        {
            path: '/bag',
            name: 'Bag',
            component: () => import('../components/bag/BagIndex.vue'),
            meta: { title: 'Bag' + initialTitle },
        },
        {
            path: '/products/:slug/:id',
            name: 'ProductsShow',
            component: () =>
                import('../components/products/ProductsShowComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/bundles/:slug/:id',
            name: 'BundlesShow',
            component: () =>
                import('../components/products/ProductsShowComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/search',
            name: 'SearchIndex',
            component: () =>
                import('../components/products/ProductsIndexComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/e-gift-card',
            name: 'eGift',
            component: () => import('../components/eGift/eGiftCard.vue'),
            meta: { title: 'eGift Card' + initialTitle },
        },
        {
            path: '/:page/:id',
            name: 'Pages',
            component: () => import('../components/pages/Pages.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/creator/:slug/:id',
            name: 'creator',
            component: () =>
                import('../views/CreatorSingleView.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/collections/:slug/:id',
            name: 'product-collection',
            component: () =>
                import('../components/products/ProductsIndexComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/a/:index/:slug/:id',
            name: 'automated-category',
            component: () =>
                import('../components/products/ProductsIndexComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/:index/:slug/:id',
            name: 'ProductsIndex',
            component: () =>
                import('../components/products/ProductsIndexComponent.vue'),
            meta: { title: 'Eichlers' },
        },
        {
            path: '/contact-us',
            name: 'ContactUs',
            component: () => import('../components/support/ContactUs.vue'),
            meta: { title: 'Contact us' + initialTitle },
        },
        {
            path: '/500',
            name: 'ServerError',
            component: () => import('../components/errorPages/ServerError.vue'),
            meta: { title: 'Server Error' + initialTitle },
        },
        {
            path: '*',
            name: '404',
            component: () => import('../components/errorPages/NotFound.vue'),
            meta: { title: 404 },
        },
    ],
});

export default router;

router.beforeEach((to, from, next) => {
    next();
    checkForUtm(to.query);
    if (to.path !== from.path) {
        document.title = to.meta.title;
        store.dispatch('customer/isLoggedIn');
        store.dispatch('zipLookUp/requetsZipCode').then(() => {
            store.dispatch('shippingRates/index');
        });
    }
});

router.afterEach((to, from) => {});

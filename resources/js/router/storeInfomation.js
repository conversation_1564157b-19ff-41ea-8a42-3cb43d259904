const initialTitle = ' | Eichlers  ';
export default [
    {
        path: '/store-information',
        name: 'StoreInformationIndex',
        component: () => import('../components/storeInformation/Index'),
        meta: { title: 'Store Information' + initialTitle },
        redirect: { name: 'StoreAddress' },
        children: [
            {
                path: 'hours',
                name: 'StoreHours',
                component: () =>
                    import('../components/storeInformation/StoreHours'),
                meta: { title: 'Store Hours' + initialTitle },
            },
            {
                path: 'address',
                name: 'StoreAddress',
                component: () =>
                    import('../components/storeInformation/StoreAddress'),
                meta: { title: 'Store Address' + initialTitle },
            },
            {
                path: 'virtual-tour',
                name: 'VirtualTour',
                component: () =>
                    import('../components/storeInformation/VirtualTour'),
                meta: { title: 'Virtual Tour' + initialTitle },
            },
        ],
    },
];

:root {
	--darkgreen: #1A4D36;
	--lightgreen: #00BC74;
	--lightblue: #12BFE8;

	--button_and_green_text: #016145;
	--page_background: #FAF8F5;
	--light_shade: #F7F2EA;
	--gold_outline: #F4E2CB;
	--header: #174834;
	--gold: #F9A73C;
	--header_texture: #1A4D36;
	--green_shade: #E8EDEB;
	--gold_shade: #F8EDDC;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

select::-ms-expand {
    display: none;
}

body {
	min-height: 100vh;
	position: relative;
    font-family: proxima-nova, sans-serif;
    min-width: 320px;
    background: var(--page_background);
}

#eichlers{
	min-height: 100vh;
	padding-bottom:332px; 
	/* display: flex;
	flex-direction: column; */
}

/* h1,h2,h3,h4,h5{
	font-size: initial;
	font-weight: initial;
} */

.only-mobile{
	display: none;
}

.only-tablet{
	display: none!important;
}

.no_scroll {
	overflow: hidden;
}

body *::-webkit-scrollbar {
    width: 0px;  /* Remove scrollbar space */
	background: transparent;  /* Optional: just make scrollbar invisible */
	height: 0px;
}

body *{
	-ms-overflow-style: none;
  }

input{
	outline: none;
	-webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

input:focus,
button:focus,
textarea:focus,
select:focus {
	outline: 0;
	-webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

a {
	text-decoration: none;
}

button{
    outline: 0;
    border: transparent;
    appearance: none;
    user-select: none;
    text-align: center;
    background-color: transparent;
    color: inherit;
    font-size: inherit;
    font-weight: inherit;
    cursor: pointer;
    font-family: inherit;
}

.heb_font {
	font-family: 'Heebo', sans-serif;
}

.lp_overview_text.heb_font {
	direction: rtl;
}

.lp_overview_text ul{
	font-size: 16px;
	padding-left: 20px;
	margin-bottom: 10px;
	padding-right: 18px;
}

.img_contain {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.img_cover {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.float_fix {
	clear: both;
}

.horizontal_spacing {
	width: 100%;
	display: inline-block;
}

.boxed_width {
	max-width: 1440px;
	position: relative;
	margin-right: auto;
	margin-left: auto;
	padding-right: 20px;
	padding-left: 20px;
}

.flex {
	display: flex;
}

.flex-sp-bet{
	justify-content: space-between;
}

.flex_center {
	justify-content: center;
	align-items: center;
}

.flex_col {
	flex-direction: column;
}

.ml_5{
	margin-left:5px;
}

.mr_5{
	margin-right:5px;
}

.mt_5{
	margin-top: 5px;
}

.mt_10{
	margin-top: 10px;
}

.mt_30 {
	margin-top: 30px;
}

.mt_60 {
	margin-top: 60px;
}

.mb_15 {
	margin-bottom: 15px;
}

.mb_21 {
    margin-bottom: 21px;
}

.mb_25{
	margin-bottom: 25px;
}

.mb_30 {
	margin-bottom: 30px;
}

.mb_50 {
	margin-bottom: 50px;
}

.pt_45 {
	padding-top: 45px;
}

.btn_style {
	display: inline-block;
	cursor: pointer;
	user-select: none;
}

.btn_style:hover {
	opacity: .9;
}

.large_btn {
	font-size: 23px;
	color: #fff;
	font-weight: 400;
	border-radius: 1px;
	padding: 15px 43.5px 13px;
}

.btn-out-stock{
    opacity:.5;
    cursor: not-allowed;
}

.small_btn {
	font-size: 16px;
	color: #fff;
	font-weight: 500;
	border-radius: 1px;
	padding: 12px 30px 10px;
}

.lg_bg {
	background-color: var(--button_and_green_text);
	border: 1px solid var(--button_and_green_text);
}

.dg_bg {
	background-color: var(--darkgreen);
	border: 1px solid var(--darkgreen);
}

.trans_bg {
	background-color: transparent;
	color: var(--darkgreen);
	border: 1px solid var(--darkgreen);
}

.lb_bg {
	background-color: var(--lightblue);
	border: 1px solid var(--lightblue);
}

.gold_bg {
	background-color: var(--gold);
	border: 1px solid var(--gold);
}

.custom_cb {
	display: none;
}

.custom_cb_checkmark {
	width: 22px;
	height: 22px;
	display: block;
	background: #fff;
	border: 1px solid var(--gold);
	border-radius: 3px;
}

.custom_cb:checked + .custom_cb_checkmark {
	background-image: url(/img/orange_check.svg);
	background-size: cover;
	background-position: center;
}

/******************
HEADER STYLES
******************/

.sticky_header {
	position: fixed;
	top: 0;
	z-index: 100;
	transition: top 0.2s ease-in-out;
	/* position: relative; */
}

.sticky_header_space {
	height: 150px;
	/* height: 40px; */
}

.hc_sticky_header_space {
	height: 85px;
}

.site_header {
	width: 100%;
	height: 100px;
	background-color: var(--darkgreen);
	background-color: #174834;
	background-image: url(/img/header_pattern.png);
	background-repeat: repeat;
	display: flex;
	align-items: center;
	padding: 0 25px;
	justify-content: space-between;
	border-bottom: 1px solid var(--gold);
	/*position: relative;*/
}

.nav-up {
	top: -100px;
}

.beta-nav-up{
	top: -139px !important;
}

.header_left,
.header_right {
	width: 300px;
	display: flex;
	align-items: center;
}

.header_left{
	justify-content: flex-start;
	margin-right: auto;
}

/*.nav_icon {
	display: none;
}*/

.nav_icon {
    width: 37px;
    height: 27px;
    padding: 5px;
    position: relative;
    display: block;
    z-index: 2;
    margin-right: 15px;
    cursor: pointer;
    user-select: none;
} 

.logo_box {
	/*margin-right: 30px;*/
}

.logo_box_link {
	display: flex;
	align-items: center;
}

.logo_icon {
    width: 44px;
    height: 42px;
}

.logo-txt{
	color: #FDFFFE;
	font-size: 11px;
	opacity: .5;
	font-style: italic;
	text-align: right;
}

.full_logo {
	width: 181px;
	margin-left: 14px;
}

.header_shipping_info,
.header_support_wrap,
.header_account_wrap {
	cursor: pointer;
	padding: 5px;
}

.header_shipping_info,
.header_support_wrap,
.header_account_wrap {
	margin-right: 50px;
    font-size: 13px;
    color: #fff;
}

.header_departments_icon {
	margin-left: 5px;
}

.search_container {
	width: 650px;
	height: 31px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-bottom: 1px solid var(--gold);
	cursor: text;
	user-select: none;
	padding-bottom: 9px;
	margin: 0 52px;
}

.search_icon {
	width: 13px;
	height: 13px;
}

.search_txt {
	font-size: 17px;
	color: var(--gold);
	margin-left: 13px;
}

.header_search_wrap {
	height: 43px;
	position: relative;
	width: 50%;
	width: calc(100% - 650px);
}

.main_searchbar {
	height: 100%;
	width: 100%;
	background: #fff;
	border: 0;
	border-radius: 4px;
	font-size: 17px;
	font-weight: 400;
	padding-left: 10px;
}

.main_searchbar::-webkit-input-placeholder {
  color: #959595;
}
.main_searchbar::-moz-placeholder {
  color: #959595;
}
.main_searchbar:-ms-input-placeholder {
  color: #959595;
}
.main_searchbar:-moz-placeholder {
  color: #959595;
}

.main_searchsubmit {
	position: absolute;
    width: 22px;
    height: 22px;
    background-image: url(/img/search.svg);
    background-position: center;
    background-size: cover;
    background-color: transparent;
    border: 0;
    cursor: pointer;
    top: 10px;
    right: 15px;
}

.header_right {
	justify-content: flex-end;
	margin-left: auto;
}

.hsi_top {
	color: #fff;
	margin-bottom: 3px;
	font-size: 13px;
	font-weight: 500;
}

.hsi_bottom {
	color: #fff;
	font-size: 13px;
}

.hsi_icon {
	margin-left: 5px;
}

.hsi_icon img,
.hat_icon img {
	width: 9.3px;
}

.header_account_wrap {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.header_account_wrap p {
	text-align: center;
}

.header_account_top {
	display: flex;
	align-items: center;
	margin-bottom: 5px;
}

.header_account_bottom p {
	color: #fff;
	font-size: 11px;
	font-weight: 400;
}

.header_cart_wrap a {
	width: 31px;
	height: 33px;
	position: relative;
	display: block;
	background-image: url(/img/bag_icon_gold.svg);
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	display: flex;
	align-items: center;
	justify-content: center;
}

.header_cart_counter {
	display: flex;
	justify-content: center;
	align-items: center;
	color: var(--gold);
	font-size: 10px;
	padding-top: 7px;
}

.abs_ship {
    position: absolute;
    width: 371px;
    height: 32px;
	background: var(--gold);
	/* background-color: #F8EDDC; */
    left: calc(50% - 185.5px);
    bottom: 0;
}

.abs_ship:before {
    content: '';
    position: absolute;
    border-right: 20px solid var(--gold);
    border-bottom: 32px solid transparent;
    border-top: 0px solid transparent;
    border-left: 25px solid transparent;
    left: -45px;
    top: 0;
}

.abs_ship:after {
    content: '';
    position: absolute;
    border-left: 20px solid var(--gold);
    border-bottom: 32px solid transparent;
    border-top: 0px solid transparent;
    border-right: 25px solid transparent;
    right: -45px;
    top: 0;
}

.mob_search_icon {
	display: none;
}

.abs_ship_inner {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
}

.abss_txt {
	font-size: 13px;
	font-weight: 500;
}

.abss_sep {
	margin: 0 7px;
}

/*Checkout Header*/
.checkout_header {
	height: 85px;
	justify-content: center;
}

/******************
END HEADER STYLES
******************/

/******************
DEPARTMENTS STYLES
******************/
.departments_wrap {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	/* height: 100vh; */
	display: flex;
	z-index: 100;
	background: rgba(0,0,0,.71);
	bottom: 0;
	/* opacity: 0;
	pointer-events: none; */
}

.dep_list_wrapper {
	background: #fff;
	width: 300px;
	height: 100%;
	background: #F9F6F4;
	position: relative;
	overflow-x: hidden;
	/* left: -100%; */
}

.dep_list_heading {
	display: flex;
    height: 53px;
    align-items: center;
    justify-content: flex-start;
    padding-left: 20px;
    padding-right: 10px;
    background: #154733;
    border-bottom: 1px solid #ddd;
}

.dep_list_heading .nav_icon.mnb_on span:before,
.dep_list_heading .nav_icon.mnb_on span:after {
	background-color: #707070
}

.dep_list_heading .logo_box {
    margin-right: 20px;
}

.dep_list_heading .header_departments p {
	color: var(--gold);
	font-size: 17px;
	font-weight: 500;
}

.dep_overlay {
	width: calc(100% - 300px);
	/*background: rgba(0,0,0,.71);*/
	position: relative;
}

.dep_list_inner {
	height: calc(100% - 75px);
	overflow: auto;
	padding-bottom: 80px;
}

/*.mobile_nav_list,
.dep_overlay_ex {
	display: none;
}*/

.mobile_nav_list {
	display: none;
}

.dep_overlay_ex {
	position: absolute;
	width: 18px;
	height: 18px;
	left: 20px;
	top: 20px;
	cursor: pointer;
}

.dep_list_single,
.dep_list_sec_head {
	height: 45px;
	width: 100%;
	padding-left: 20px;
	padding-right: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
	user-select: none;
	transition: .2s;
}

.dep_list_single:hover,
.dep_list_single_selected {
	background: var(--gold_shade);
}

.dls_ttl {
	font-size: 15px;
	color: #000;
	font-weight: 500;
	width: 100%;
    height: 100%;
    display: flex;
    justify-content: start;
    align-items: center;
}

.dls_arrow {
	width: 6px;
	height: 10px;
}

.dep_list_sec_head {
	border-top: 1px solid var(--gold_outline);
	border-bottom: 1px solid var(--gold_outline); 
}

.dlsh_ttl {
	color: var(--button_and_green_text);
	font-size: 17px;
	font-weight: 500;
}

/**********************
END DEPARTMENTS STYLES
**********************/

/******************
DROPDOWN STYLES
******************/

.hdd_wrap {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	display: flex;
	z-index: 100;
	display: none;
	pointer-events: none;
}

.hdd_clickable_wrapper {
    position: absolute;
    background: #F9F6F4;
    border: 1px solid #FBA82B;
    top: 75px;
    z-index: 2;
    border-radius: 2px;
    box-shadow: 0 3px 6px rgba(0,0,0,.16);
    padding: 25px;
    border-radius: 1px;
}

/*.hdd_clickable_wrapper:after {
	content: '';
	position: absolute;
	top: 0;
	left: 50%;
	width: 0;
	height: 0;
	border: 15px solid transparent;
	border-bottom-color: #fff;
	border-top: 0;
	margin-left: -15px;
	margin-top: -15px;
}*/

.account_header_list {
    right: 177px;
    padding: 5px 0;
    width: 135px;
    /*opacity: 0;
    pointer-events: none;*/
}

.ahl_single {
    /*margin-bottom: 20px;*/
    /*padding: 0 33px;*/
}

.ahl_single:hover {
    background: var(--gold_shade)
}

.ahl_single:last-child {
    margin-bottom: 0;
}

.ahl_link {
    font-size: 13px;
    color: #000;
    width: 100%;
    padding: 12px 0;
    display: block;
    text-align: center;
    font-weight: 500;

}

.hdd_shipping {
	width: 226px;
	height: 146px;
	right: 30px;
    padding: 20px 30px;
	display: flex;
    flex-direction: column;
    /*opacity: 0;
    pointer-events: none;*/
}
 
.hdds_txt {
	text-align: center;
	font-size: 13px;
	font-weight: 500;
	margin-bottom: 4px;
}

.hdds_txt span {
	color: var(--button_and_green_text);
}

.hdds_number {
	font-size: 21px;
	font-weight: 500;
	text-align: center;
	color: var(--button_and_green_text);
	margin-bottom: 5px;
}

.hdds_email {
    font-size: 15px;
    font-weight: 500;
    color: var(--button_and_green_text);
}

.hdd_overlay {
    width: 100%;
    background: rgba(0,0,0,.71);
    /* height: calc(100vh - 100px); */
    top: 99px;
    position: fixed;
    pointer-events: initial;
	left: 0;
	bottom: 0;
}

.search_ui_wrap {
	position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    z-index: 100;
    align-items: center;
    /*g*/
}

.search_ui_box {
	width: 100%;
	max-width: 660px;
	background: #F9F6F4;
	border: 1px solid var(--gold);
	border-radius: 1px;
    position: absolute;
    z-index: 10;
    top: 25px;
    left: calc(50% - 330px);
    /*pointer-events: none;*/
    /*opacity: 0;
    padding: 0;*/
}

.sub_top {
	padding: 15px;
	display: flex;
}

.sub_search_input {
    width: calc(100% - 38px);
    padding-left: 15px;
    font-size: 17px;
    font-weight: 300;
    background: transparent;
    border: 0;
    height: 100%;
}

.sub_bottom {
	border-top: 1px solid var(--gold_outline);
	padding: 15px 0;
	max-height: 300px;
	overflow: auto;
}

.sub_suggest {
	font-size: 17px;
	font-weight: 300;
	/*margin-bottom: 15px;*/
	cursor: pointer;
	padding-left: 48px;
	padding: 8px 0 8px 45px;
}

.sub_suggest:hover {
	background: var(--gold_shade);
}

.sub_suggest:last-child {
	margin-bottom: 0;
}

.sub_suggest span {
	font-weight: 500;
}

.su_overlay {
    width: 100%;
    background: rgba(0,0,0,.71);
    height: calc(100vh - 100px);
    top: 100px;
    position: absolute;
    pointer-events: initial;
}

/**********************
END DROPDOWN STYLES
**********************/


/******************
BREADCRUMBS STYLES
******************/

.breadcrumbs_wrapper .boxed_width {
	display: flex;
	justify-content: space-between;
}

.inner_breadcrumbs {
	width: 80%;
}

.breadcrumbs_wrapper{
	padding: 10.5px 0;
    position: absolute;
    bottom: -42px;
    width: 100%;
    left: 0;
    background: var(--page_background);
}

.single_bc_link,
.single_bc_icon {
    color: #000;
    font-size: 16px;
    font-weight: 300;
}

.single_bc_icon {
	margin: 0 5px;
}

.single_bc_active {
	font-weight: 500;
}

.language_wrap {
	display: flex;
	margin-left: auto;
}

.lang_single {
	width: 42px;
	height: 21px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: var(--gold);
	background: #fff;
	cursor: pointer;
	user-select: none;
	font-size: 11px;
	font-weight: 500;
    border: 1px solid var(--gold);
    transition: .2s;
}

.lang_active {
	background: var(--gold);
	color: #fff;
}

.lang_eng {
	border-radius: 30px 0 0 30px;
}

.lang_heb {
	border-radius: 0 30px 30px 0;
}

/******************
END BREADCRUMBS 
******************/

/******************
SINGLE PRODUCT
******************/

.large_product_wrapper {
	background: #fff;
}

.large_product_top {
	padding-top: 30px;
	width: 100%;
	display: flex;
	align-items: flex-start;
	/*border-bottom: .25px solid #959595;*/
}

.mobile_product_title {
	display: none;
}

.large_product_images {
	width: calc(50% + 37.5px);
	display: flex;
	align-items: flex-start;
	margin-bottom: 30px;
    position: sticky;
    top: 42px;
    transition: top 0.2s ease-in-out;
}

.large_product_images.lpi_down {
	top: 100px;
}

.lpi_thumb_single {
	width: 75px;
	height: 75px;
	margin: 10px 0;
	border: 1px solid var(--gold_outline);
	cursor: pointer;
	user-select: none;
}

.lpi_thumb_single.lpi_active {
	border-color: var(--gold);
}

.lpi_thumb_single > img {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.lpi_main {
	padding-right: 40px;
	padding-left: 20px;
	width: calc(100% - 75px);
	height: 500px;
	user-select: none;
	position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lpim_has_prev {
	padding-top: 35px;
}

.lpim_preview {
	background: var(--button_and_green_text);
	width: 148px;
	height: 27px;
	border-radius: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	font-size: 15px;
	font-weight: 400;
	position: absolute;
	top: 0;
	z-index: 1;
	cursor: pointer;
	user-select: none;
}

.lpim_preview:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: var(--button_and_green_text);
    border-bottom: 0;
    margin-left: -8px;
    margin-bottom: -7px;
}

.lpi_main img {
	max-width: 100%;
	max-height: 100%;
	/* width: 100%;
	height: 100%; */
	object-fit: contain;
	object-position: top center;
}

.cl-t-zm-wrp{
	cursor: zoom-in;
	border: solid #BABABA 1px;
	border-radius: 40px;
	padding: 3px 6px;
	display: flex;
	align-items: center;
	margin-top: -3px;
    background: white;
    opacity: .8;
}

.zm-icon{
	height: 14px;
	width: 14px;
}

.cl-t-zm-txt{
	color: #000000;
	font-size: 11px;
	margin-left: 3px;
}

.large_product_right {
	/*padding-left: 20px;*/
	width: calc(50% - 37.5px);
}

.large_product_vendor {
	font-size: 16px;
	font-weight: 300;
	color: var(--header_texture);
	margin-bottom: 10px;
}

.large_product_title {
	font-size: 36px;
	font-weight: 500;
	margin-bottom: 15px;
}

.large_product_under_title {
	font-size: 17px;
	margin-bottom: 15px;
	line-height: 20px;
	margin-top: -10px;
}

.large_product_under_title a {
	color: var(--button_and_green_text);
}

.large_product_label {
	background: #0074FC;
	border-radius: 50px;
	font-size: 12px;
	font-weight: 500;
	padding: 6px 20px;
	display: inline-block;
	color: #fff;
	margin-bottom: 15px;
}

.large_product_highlight {
	margin-bottom: 15px;
}

.large_product_highlight p {
	font-size: 16px;
	font-weight: 200;
	margin-bottom: 5px;
	line-height: 26px;
}

.large_product_highlight p:last-child {
	margin-bottom: 0;
}

.large_product_section {
	margin-bottom: 15px;
	padding-bottom: 15px;
	border-bottom: .25px solid var(--gold_outline);
}

/*.large_product_section:last-child,*/
.large_product_cart_section {
	border-bottom: 0;
	margin-bottom: 0;
}

.mobile_product_price_section {
	display: none;
}

.large_product_price_section {
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.lpps_main_price {
	font-size: 20px;
	font-weight: 500;
}

.lpps_sale_prce {
	font-size: 16px;
	font-weight: 200;
	color: #959595;
	text-decoration: line-through;
	margin-left: 10px;
}

.lpps_shipping {
	font-size: 16px;
	font-weight: 500;
	color: var(--button_and_green_text);
	margin-left: 10px;
}

.large_product_options_section {
	display: flex;
	align-items: flex-start;
	flex-direction: column;
}

.lpos_title {
	width: 175px;
	font-size: 18px;
	font-weight: 200;
	text-align: left;
	/*padding-top: 10px;*/
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.lpos_title_has_action {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.lpos_title_has_action .lpo_btn {
	margin-bottom: 0;
	margin-right: 0;
}

.large_product_dropdown_section {
	padding-bottom: 25px;
}

.lpos_dropdown {
	min-width: 215px;
	height: 56px;
	border: 1px solid var(--gold_outline);
	appearance: none;
	-webkit-appearance: none;
	text-align: center;
	background: #fff;
	border-radius: 1px;
	font-size: 23px;
	font-weight: 200;
    background-image: url(/img/carret_down.png);
    background-size: 13px;
    background-position: 97% center;
    background-repeat: no-repeat;
	padding-left: 12px;
	padding-right: 20px;
}

.info_icon {
	width: 15px;
	height: 15px;
	border-radius: 50%;
	margin-left: 5px;
	cursor: pointer;
	user-select: none;
	display: inline-block;
	position: relative;
}

.info_icon img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.tooltip_cont {
    visibility: hidden;
    width: 200px;
    background-color: var(--light_shade);
    border: 1px solid var(--gold_shade);
    color: #000;
    text-align: center;
    border-radius: 1px;
    padding: 15px;
    position: absolute;
    z-index: 1;
    top: -15px;
    left: 25px;
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 3px 6px rgba(0,0,0,.16);
}

.tooltip_cont p {
	font-size: 14px;
    font-weight: 300;
    line-height: 22px;
}

.tooltip_cont::before {
    content: "";
    position: absolute;
    top: 14px;
    right: 100%;
    margin-top: -7px;
    border-width: 12px;
    border-style: solid;
    border-color: transparent var(--gold_shade) transparent transparent;
}

.tooltip_cont::after {
    content: "";
    position: absolute;
    top: 14px;
    right: 100%;
    margin-top: -5px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent var(--light_shade) transparent transparent;
}

.info_icon img:hover + .tooltip_cont,
.tooltip_cont:hover {
  visibility: visible;
  opacity: 1;
}

/*.tooltip_cont {
 	visibility: visible;
  	opacity: 1;
}*/

.lpos_options {
	width: calc(100% - 175px);
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	overflow: hidden;
}

.lpo_btn {
	padding: 10px 20px 7px;
	border: 1px solid var(--gold_outline);
	border-radius: 1px;
	margin: 0 15px 15px 0;
	cursor: pointer;
	user-select: none;
	
}

.lpo_btn:hover {
	border-color: var(--gold); 
}

.lpo_btn:hover > p:first-child {
	/*color: var(--gold);*/
}

.lpo_btn.lpo_btn_selected:hover > p {
	color: #000;
}

.lpo_btn_selected {
    box-shadow: 0 0 0 1px var(--gold);
    border-color: var(--gold);
    border-radius: .5px;
}

.lpob_one_line {
	font-size: 18px;
	font-weight: 200;
	display: flex;
    align-items: center;
}

.lpob_top_line {
	font-size: 15px;
	text-align: center;
	margin-bottom: 5px;
	font-weight: 200;
}

.lpob_bottom_line {
	margin-top: 6px;
	font-size: 17px;
	font-weight: 500;
	text-align: center;
}

.opt-instructions > a{
	 color: var(--header_texture);
}

.personalize_section {
	display: flex;
	align-items: center;
	/*flex-wrap: wrap;*/
}

.personalize_section .large_btn {
	min-width: 222px;
}

.personalize_desc {
	font-size: 14px;
	font-weight: 500;
	margin-left: 35px;
}

.lpob_ex {
	font-size: 26px;
	margin: 0 3px;
}

.lpo_btn_noborder {
	border: 0;
    padding-right: 0;
    padding-left: 0;
}

.lpob_chart_link {
	font-size: 15px;
	font-weight: 500;
	color: var(--button_and_green_text);
	text-decoration: underline;
}

.large_product_addons_section {
	display: flex;
	flex-wrap: wrap;
}

.large_product_addons_section .lpos_options {
	width: 100%;
}

.lpas_title {
	font-size: 18px;
	font-weight: 500;
	width: 100%;
	margin-bottom: 25px;
}

.lpob_with_image {
	padding: 2px;
	display: flex;
	align-items: center;
	width: 270px;
	width: calc(50% - 15px);
	min-width: 250px;
}

.lpo_btn_img {
	width: 60px;
	height: 72px;
	border-radius: 1px;
	/*overflow-x: hidden;*/
	margin-bottom: auto;
}

.lpo_btn_img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.lpobi_text {
	padding: 5px 15px;
	width: calc(100% - 60px);
}

.lpob_with_image.lpo_btn_selected {
	box-shadow: 0 0 0 1px var(--gold);
	border-color: var(--gold);
	border-radius: .5px;
}

.large_product_ship_section {
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}

.ship_icon {
	width: 38px;
}

.ship_icon img {
	width: 38px;
}

.ship_info_side {
	margin-left: 15px;
}

.ship_top_text {
	font-weight: 500;
	margin-bottom: 25px;
}

.ship_top_text span {
	text-decoration: underline;
	cursor: pointer;
}

.ship_option {
	margin-bottom: 19px;
}

.ship_option:last-child {
	margin-bottom: 0;
}

.ship_option_name {
	font-size: 17px;
	font-weight: 500;
}

.ship_option_sub {
	font-size: 15px;
	font-weight: 300;
}

.ship_option_sub span {
	color: var(--button_and_green_text);
	font-weight: 500;
}

.large_product_pickup_section {
	align-items: center;
}

.pickup_icon img {
	width: 20px;
}

.pickup_top_text {
	margin-bottom: 0;
}

.pickup_top_text span {
	font-size: 15px;
	font-weight: 200;
	text-decoration: none;
}

.plcs_ttl {
	font-size: 18px;
	font-weight: 500;
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
}

.dropdown_icon {
	width: 20px;
	height: 15px;
	background-image: url(/img/carret_down.png);
	background-size: contain;
}

.large_product_cart_section-inner {
	display: flex;
	align-items: center;
}

.cart_amount_select {
    width: 71px;
    height: 56px;
    font-size: 18px;
    background: #fff;
    font-weight: 200;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: 1px solid var(--button_and_green_text);
    /* padding-left: 18px; */
    background-image: url(/img/carret_down_grey.png);
    background-size: 20px;
    background-repeat: no-repeat;
    background-position: center right 8px;
    margin-right: 10px;
    border-radius: 1px;
}

.wishlist_btn {
    margin-left: 20px;
    background-image: url(/img/gold_heart.svg);
    width: 30px;
    height: 26px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
}

.wishlist_btn.wished {
	background-image: url(/img/gold_heart_fill.svg);
}

.subscribe_save_box {
	background: var(--light_shade);
	padding: 20px;
	border-radius: 2px;
	width: 100%;
	margin-bottom: 25px;
}

.sas_top {
	display: flex;
	align-items: center;
}

.sast_txt {
	margin-left: 15px;
}

.sast_ttl {
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 5px;
	display: block;
	cursor: pointer;
	user-select: none;
}

.sast_price {
	color: var(--button_and_green_text);
	font-size: 14px;
	font-weight: 500;
}

.sas_bottom {
	margin-top: 15px;
	display: none;
}

.sas_desc {
	font-size: 14px;
	line-height: 19px;
	margin-bottom: 10px;
}

.sas_desc span {
	color: var(--button_and_green_text);
}

.sas_shipping {
	font-size: 14px;
	line-height: 19px;
	font-weight: 500;
	margin-bottom: 10px;
}

.sas_btn {
	background: var(--gold);
	color: #fff;
	margin-top: 20px;
}

.large_product_overview {
	width: 100%;
	padding: 35px 25px;
	border-radius: 2px;
	background: var(--light_shade);
}

.large_product_overview {
	font-size: 22px;
	font-weight: 300;
	/* margin-bottom: 10px; */
	/* \ */
}

.large_product_overview .boxed_width {
	max-width: 900px;
}

.large_product_overview_ttl {
	font-size: 30px;
	margin-bottom: 23px;
	font-weight: 500;
	text-align: center;
}

.lp_overview_text {
	max-height: 170px;
	overflow: hidden;
}

.lp_overview_text p {
	font-size: 16px;
	line-height: 29px;
	margin-bottom: 10px;
}

.lp_overview_btn {
	text-align: center;
	cursor: pointer;
	user-select: none;
	color: var(--button_and_green_text);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 30px;
}

.lp_overview_btn span {
	margin-left: 5px;
	font-size: 16px;
}

.product_details_section {
	margin-top: 20px;
}

.product_details_inner > .product_track_ttl {
	margin-bottom: 20px;
}

.product_details_inner {
	max-width: 70%;
}

.product_details_lines {
	border-top: 1px solid var(--gold_outline);
}

.product_single_detail {
	display: flex;
	border-bottom: 1px solid var(--gold_outline);
	padding: 15px 0;
}

.psd_name {
	width: 175px;
}

.psd_name p {
	font-size: 16px;
	font-weight: 500;
	text-align: left;
}

.psd_value {
	width: calc(100% - 175px);
}

.psd_value p {
	font-size: 16px;
	font-weight: 200;
	text-align: left;
}

.author_section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.author_left {
	width: calc(100% - 370px);
	padding-right: 25px;
}

.author_left .product_track_ttl {
	margin-bottom: 20px;
}

.author_text p {
	font-size: 16px;
	font-weight: 200;
	margin-bottom: 10px;
	line-height: 26px;
}

.author_right {
	width: 370px;
}

.follow_box {
	margin-bottom: 10px;
}

.follow_box:last-child {
	margin-bottom: 0;
}

.follow_box_ttl {
	font-size: 17px;
	font-weight: 500;
	margin-bottom: 15px;
	margin-top: 15px;
}

.follow_single_entry {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.follow_single_entry:last-child {
	margin-bottom: 0;
}

.fse_img {
	width: 45px;
	height: 45px;
	border-radius: 50px;
	overflow: hidden;
	margin-right: 7px;
	/* background: #D8D8D8; */
}

.fse_img img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	object-position: center;
}

.fse_name {
	color: var(--button_and_green_text);
	font-size: 15px;
	font-weight: 500;
	margin-right: 15px;
	width: 120px;
}

.btn_follow,
.btn_following {
	/*margin-left: auto;*/
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 115px;
    height: 37px;
}

.btn_follow {
	border: 1px solid var(--button_and_green_text);
	color: var(--button_and_green_text);
	background: #fff;
}

.btn_following {
	background: var(--button_and_green_text);
	color: #fff;
}

/******************
PRODUCT TRACK
******************/

.product_track {
	/*box-shadow: 0 0px 6px rgba(0,0,0,.16);*/
	background: #fff;
	border: .25px solid var(--gold_outline);
}

.product_track_head {
	width: 100%;
	padding: 20px 15px;
	border-bottom: .25px solid var(--gold_outline);
    display: flex;
    justify-content: space-between;align-items: center;
}

.product_track_ttl {
	font-size: 25px;
	font-weight: 500;
}

.product_track_btn {
	background: var(--gold);
	color: #fff;
	width: 103px;
	height: 40px;
	cursor: pointer;
	user-select: none;
	border-radius: 1px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: 500;
}

.product_track_btn:hover{
	opacity: .8;
}

.product_track_body {
	display: flex;
	overflow: hidden;
	position: relative;
	min-height: 280px;
}

.product_track_arrow {
	width: 51px;
	height: 90px;
	position: absolute;
	background: #fff;
	/*box-shadow: 0 0 6px rgba(0,0,0,.16);*/
	border: 1px solid var(--gold_outline);
	background-position: center;
	background-repeat: no-repeat;
	background-size: 12px 25px;
	z-index: 10;
	cursor: pointer;
	user-select: none;
	top: calc(50% - 45px);
}

.product_track_arrow:hover {
	/*box-shadow: 0 0 6px rgba(0,0,0,.46);*/
	background-color: var(--gold_outline);
}

.pta_left {
	background-image: url(/img/arrow_left_gold.svg);
	left: 0;
	border-radius: 0 4px 4px 0;
	/*border-left: 0;*/
}

.pta_right {
	background-image: url(/img/arrow_right_gold.svg);
	right: 0;
	border-radius: 4px 0 0 4px;
	border-right: 0;

}

.pta_left:hover {
	background-image: url(/img/arrow_left_white.svg);
}

.pta_right:hover {
	background-image: url(/img/arrow_right_white.svg);
}

.product_track_inner {
	display: flex;
	position: relative;
	/* overflow: hidden; */
	overflow: auto;
}

.product_track_scroller {
	display: flex;
	position: relative;
}

.track_product {
    display: inline-block;
}

.track_product a {
	width: 205px;
	padding: 20px;
	display: flex;
	flex-direction: column;
}

.track_product_image {
	/* width: 130px;
	height: 130px; */
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 5px;
	display: flex;
	justify-content: center;
}

.track_product_image img {
	/* max-width: 100%;
	max-height: 100%; */
	width: 130px;
	height: 130px;
	object-fit: contain;
	object-position: center;
	transition: transform .1s;
}

.track_product_label-wrap{
	height: 21px;
	z-index: 2;
	margin-bottom: 5px;
}

.track_product_label{
	font-size: 11px;
	text-transform: uppercase;
	border-radius: 50px;
	padding: 4px 11px;
	color: white;
	/* margin-bottom: 5px;
	width: fit-content; */
	display: inline-block;
}

.track_product a:hover .track_product_image img{
	-ms-transform: scale(1.1); /* IE 9 */
	-webkit-transform: scale(1.1); /* Safari 3-8 */
	transform: scale(1.1);
}

.track_product_brand {
	font-size: 12px;
	font-weight: 300;
	line-height: 20px;
	color: var(--header_texture);
	overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.track_product_title {
	font-size: 15px;
	color: #000;
	font-weight: 500;
	line-height: 20px;
	height: 40px;
	margin-bottom: 10px;
	display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track_product_price_wrapper {
	display: flex;
	align-items: center;
	margin-bottom: 5px;
}

.track_product_price {
	font-size: 17px;
	color: #000;
	line-height: 20px;
	font-weight: 300;
}

.track_product_fake_price {
	font-size: 15px;
	font-weight: 300;
	color: #959595;
	text-decoration: line-through;
	margin-left: 8px;
}

.track_product_currency_wrapper {
	display: flex;
	align-items: center;
}

.track_product_fake_currency {
	margin-left: 8px;
	color: #959595 !important;
	text-decoration: line-through;
}

/* .track_product a:hover 
.track_product_title ,
.track_product a:hover 
.track_product_price{
	color: #016145;
} */

.track_product a:hover > *{
	color: #016145;
}

/******************
END PRODUCT TRACK
******************/

/******************
LIGHTBOX STYLES
******************/

.lb_overlay {
	width: 100%;
	/* height: 100vh; */
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	background: rgba(255,255,255,.95);
	z-index: 1000000;
	/*display: none;*/
}

.lb_close {
	position: fixed;
	top: 25px;
	right: 25px;
	font-size: 40px;
	font-weight: 200;
	color: #000;
	cursor: pointer;
	width: 45px;
	height: 45px;
	display: flex;
	justify-content: center;
}

.lb_arrow {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #fff;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    top: calc(50% - 15px);
}

.lb_arrow img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.lba_left {
	left: 25px;
}

.lba_right {
	right: 25px;
}

.lb_center_section {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	background: white;
}

.lb_main_image {
	height: 90vh;
	width: 90vw;
	max-width: 1500px;
	max-height: 1500px;
	display: flex;
	justify-content: center;
}

.lb_main_image img {
	max-width: 100%;
	max-height: 100%;
	/* width: 100%;
	height: 100%; */
	object-fit: contain;
}

/*
USER AREA
*/

.user_area_wrapper {
	display: flex;
	align-items: flex-start;
	padding-top: 40px;
	width: 100%;
	/* min-height: 100vh; */
}

.user_sidebar {
	width: 210px;
	display: flex;
	flex-direction: column;
	padding-left: 20px;
}

.user_content_wrapper {
	width: calc(100% - 210px);
	padding-left: 10px;
	padding-right: 10px;
}

.us_link {
	font-weight: 500;
	font-size: 16px;
	margin-bottom: 25px;
	color: #000;
}

.us_link_active {
	color: var(--button_and_green_text);
}

.us_links_inside {
	display: flex;
	flex-direction: column;
}

.us_links_inside .us_link {
	padding-left: 15px;
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 15px;
}

.us_links_inside .us_link:last-child {
	margin-bottom: 25px;
}

.usc_center {
	max-width: 1125px;
	margin-left: auto;
	margin-right: auto;
	position: relative;
}

.usc_ttl_sec {
	margin-bottom: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.usc_ttl {
	font-size: 36px;
	font-weight: 500;
}

.usc_sort_wrapper {
	display: flex;
	margin-left: auto;
}

.usc_sort {
	font-size: 16px;
	font-weight: 500;
	margin-left: 50px;
}

.usc_sort:first-child {
	margin-left: 0;
}

.usc_sort select {
    background: transparent;
    border: 0;
    font-size: 16px;
    font-family: 'ProximaNova', sans-serif;
    font-weight: 300;
    background-image: url(/img/grey_arrow_down.svg);
    background-repeat: no-repeat;
    background-position: right;
    padding-right: 17px;
    background-size: 11px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.user_card {
	background: #fff;
	border-radius: 1px;
	margin-bottom: 30px;
	border: 1px solid var(--gold_outline);
}

.page_orderhist .user_card {
	padding: 0 20px;
}

.uc_head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid var(--gold_outline);
	padding: 10px 10px 10px 30px;
}

.page_orderhist .uc_head {
	padding: 10px 0 10px 0;
}

.uc_head_ttl {
	font-size: 22px;
	font-weight: 500;
}

.uc_head_btn {
	background: var(--button_and_green_text);
	font-size: 15px;
	font-weight: 500;
	color: #fff;
	border-radius: 1px;
	padding: 12.5px 18px;
	cursor: pointer;
	user-select: none;
	transition: .3s;
	margin-left: auto;
}

.uc_cancel_btn,
.uc_update_btn {
	width: 125px;
	text-align: center;
}

.uc_cancel_btn {
	background: #BFBFBF;
	margin-top: 10px;
}

.uc_head_btn.uhb_tran {
	border: 1px solid var(--button_and_green_text);
	color: var(--button_and_green_text);
	background: #fff;
}

.uc_head_btn + .uc_head_btn {
	margin-left: 10px;
}

.uc_head_btn:hover {
	color: #fff;
	background: var(--darkgreen);
}

.uc_cancel_btn:hover {
	background: #9f9f9f;
}

.uc_head_btn.uhb_noborder:hover {
	background: #fff;
	color: var(--button_and_green_text);
}

.uc_body {
	padding: 30px;
}

.profile_part {
	margin-bottom: 20px;
}

.profile_part:last-child {
	margin-bottom: 0;
}

.pro_part_ttl {
	font-size: 15px;
	font-weight: 300;
	margin-bottom: 5px;
}

.pro_part_txt {
	font-size: 20px;
}

.uc_card_body {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
    width: calc(100% + 24px);
    margin-left: -12px;
}

.ucb_card {
	height: 160px;
	border: 1px solid var(--gold_outline);
	padding: 20px 20px 10px;
	width: calc(33% - 24px);
	margin: 0 12px 24px;
	display: flex;
	flex-direction: column;
}

.ucb_card:nth-last-child(-n+3) {
    margin-bottom: 0;
}

.ucb_card_top {
	margin-bottom: 20px;
}

.card_dark_txt {
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 7px;
}

.card_light_txt {
	font-size: 14px;
	font-weight: 300;
	margin-bottom: 7px;
}

.ucb_card_bot {
	display: flex;
	margin-top: auto;
}

.ucb_card_bot .card_dark_txt {
	margin-bottom: 0;
}

.ucb_actions {
	display: flex;
	margin-left: auto;
}

.ucb_edit {
	color: var(--button_and_green_text);
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	user-select: none;
}

.ucb_sep {
	color: var(--button_and_green_text);
	margin: 0 5px;
	font-size: 14px;
	font-weight: 500;
}

.ucb_del {
	color: var(--button_and_green_text);
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	user-select: none;
}

.cdt_red {
	color: #E30F26;
}

.order_number {
	font-weight: 300;
	color: black;
}

.uc_order_head {
	padding: 20px 0 20px 0px;
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	border-bottom: 1px solid var(--gold_outline);
}

.uoh_sec {
	font-size: 15px;
}

.uohs_ttl {
	font-weight: 300;
}

.uohs_ttl span {
	font-weight: 600;
}

.uohs_txt {
	font-weight: 500;
	margin-top: 5px;
}

.uhb_noborder {
	font-size: 15px;
	color: var(--button_and_green_text);
	font-weight: 500;
	/*margin-left: auto;*/
	background: #fff;
}

.uohs_extra {
	font-weight: 300;
	margin-top: 7px;
}

.uoh_md {
	margin: 11px 0 auto 20px;
}

.uoh_md .uohs_ttl {
	cursor: pointer;
	user-select: none;
}

.uc_order_item {
	 display: flex;
	 align-items: flex-start;
	 padding: 20px 0 20px 0;
	 border-bottom: 1px solid var(--gold_outline);
}

.page_fav .uc_order_item,
.page_buyagain .uc_order_item {
	padding: 20px;
}

.page_fav .uc_order_item:last-child,
.page_buyagain .uc_order_item:last-child {
	border-bottom: 0;
}

/*.uc_order_item:last-child {
	border-bottom: 0;
}*/

.ucoi_thumb {
	width: 70px;
	height: 80px;
}

.ucoi_thumb img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.ucoi_info {
	margin-left: 30px;
	margin-right: auto;
}

.ucoi_one {
	color: #959595;
	font-size: 15px;
	font-weight: 300;
	margin-bottom: 10px;
}

.ucoi_two {
	font-size: 16px;
	color: #000;
	font-weight: 500;
	margin-bottom: 5px;
}

.ucoi_three,
.ucoi_four {
	display: flex;
}

.ucoi_three {
	margin-top: 5px;
	margin-bottom: 15px;
}

.ucoi_qty,
.ucoi_four p {
	font-size: 15px;
	font-weight: 300;
	color: #000;
	line-height: 20px;
}

.ucoi_price {
	font-size: 15px;
	font-weight: 500;
	color: #000;
}

.ucoi_four p:last-child {
	/* margin-left: 5px;
	padding-left: 5px; */
	/* border-left: 1px solid #000; */
}

.ucoi_actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.ucoi_order_info {
	display: flex;
	align-items: flex-start;
	padding: 25px 20px;
}

.ucoioi_ship p,
.ucoioi_bill p {
	margin-bottom: 5px;
	font-size: 15px;
}

.ucoioi_bill {
	margin-left: 75px;
}

.ucoi_thin {
	font-weight: 300;
}

.ucoi_bold {
	font-weight: 600;
}

.ucoioi_totals {
	margin-left: auto;
	width: 282px;
}

.ucoioi_totals p {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 15px;
}

.ucoioi_totals p:last-child {
	margin-bottom: 0;
}

.ucoi_sep {
	width: 100%;
	height: 1px;
	background: var(--gold_outline);
}

.acts_2 {
	margin-top: 20px;
	display: flex;
	align-items: center;
	cursor: pointer;
}

.acts_2 p {
	font-size: 14px;
	font-weight: 300;
    line-height: 10px
}

.acts_2 p:hover{
	color: #016145;
}

.acts_2 p:last-child {
	margin-left: 5px;
	padding-left: 5px;
	/* border-left: 1px solid #000; */
}

.fav_price {
	font-size: 20px;
	margin-left: 0;
}

.acts_2 .fav_remove,
.acts_2 .fav_remove:last-child {
	font-size: 15px;
	font-weight: 300;
	border: 0;
}

.buyagain_four p {
	font-size: 13px;
}

.buyagain_past {
	width: 105px;
	height: 26px;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #F8EDDC;
	border-radius: 30px;
	color: #FCAD00;
	font-size: 15px;
	font-weight: 500;
	margin-top: 10px;
}

.sub_past {
	min-width: 185px;
}

.page_buyagain .ucoi_two {
	margin-bottom: 5px;
}

.page_buyagain .buyagain_four {
	margin-bottom: 5px;
}

/*
Products Styles
*/

.products_wrapper {
    display: flex;
    align-items: flex-start;
    padding-top: 40px;
    background: #F9F6F4;
    width: 100%;
	min-height: 100vh;
	/* margin-bottom: 50px; */
}

.products_sidebar {
    width: 236px;
    display: flex;
    flex-direction: column;
    padding-left: 27px;
}

.psb_main_ttl {
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 18.5px;
	padding-bottom: 18.5px;
	border-bottom: 1px solid #ccc;
}

.psb_sec {
	margin-bottom: 18.5px;
	padding-bottom: 18.5px;
	border-bottom: 1px solid #ccc;
}

.psb_sec:last-child {
	border-bottom: 0;
}

.psb_sec_ttl {
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 18.5px;
}

.psb_cb_sec_single {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.psb_cb {
	display: none;
}

.psb_cb_box {
	width: 14px;
	height: 14px;
	display: inline-flex;
	border: 1px solid #707070;
	border-radius: 1px;
}

.psb_cb:checked + .psb_cb_box {
	background: var(--button_and_green_text);
}

.psb_cb_text {
	font-size: 15px;
	margin-left: 10px;
	cursor: pointer;
	user-select: none;
}

.psb_btn_sec_inner {
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	justify-content: space-between;
}

.psb_btn_sec_inner .lpo_btn {
	background: #fff;
    padding: 6px 18.8px 3px;
    border-radius: 1px;
    margin: 0 0px 10px 0;
}

.psb_range_single {
	display: flex;
	align-items: center;
}

.psb_range_box {
    display: flex;
    border: 1px solid var(--gold_outline);
    width: 75px;
    height: 37px;
    background: #fff;
    border-radius: 1px;
    align-items: center;
}

.psb_range_dollar {
	font-size: 18px;
	font-weight: 300;
	color: var(--gold);
	padding-left: 9px;
	padding-right: 3px;
}

.psb_range_input {
	width: calc(100% - 22.52px);
	border: 0;
	font-size: 18px;
}

.psb_range_to {
	margin-left: 6px;
	margin-right: 6px;
}

.psb_range_go {
	color: var(--button_and_green_text);
	user-select: none;
	cursor: pointer;
	font-size: 15px;
	margin-left: auto;
}

.products_content_wrapper {
	width: calc(100% - 236px);
	padding-left: 15px;
    padding-right: 15px;
}

.prc_center {
    max-width: 1125px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

.products_grid_wrapper {
	display: flex;
	flex-wrap: wrap;
}

.product_item-grid {
	width: calc(25% - 5px);
	margin: 2.5px;
	background: #fff;
	display: flex;
	flex-direction: column;
	border-radius: 4px;
	position: relative;
}

.product_item-grid a{
	display: block;
	height: 100%;
}

.product_item-grid  .pig_link{
	display: flex;
	flex-direction: column;
	height: inherit;
}

.product_item-grid .pig_info{
	margin-top: auto;
}

.pig_tag {
	font-size: 12px;
	font-weight: 500;
	padding: 5px 10px 5px 0;
	border-radius: 0 0 13px 0;
	position: absolute;
	top: 0;
	left: 0;
	color: white;
	z-index: 2;
	background: #F9A73D;
}

.pig_tag span{
	padding: 5px 10px;
	color: white;
}

.pigt_pick {
	background: #F9A73D;
	color: var(--button_and_green_text);
}

.pigt_trend {
	background: #8B0B19;
	color: #fff;
}

.pigt_new {
	background: var(--button_and_green_text);
	color: #fff;
}

.pig_images_wrapper {
	padding: 15px;
	/* height: 270px; */
	height: 225px;
}

.pig_images_wrapper .pig_wish{
	display: none;
}

.pig_add_imgs {
	display: flex;
	padding: 0 15px;
}

.pig_img_main {
	width: 100%;
	/* height: calc(100% - 45px); */
	height: 100%;
	transition: transform .1s;
}

.pig_img_main img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	object-position: center
}

.img-indicators{
	display: none;
}

.pigai_single {
	width: calc(40px - 5px);
	height: 40px;
	margin: 5px;
	margin-bottom: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.pigai_single img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	object-position: center;
}

.pigai_single p {
	font-size: 16px;
	font-weight: 500;
	color: #000;
}

.pig_vendor {
	font-size: 13px;
	font-weight: 300;
	color: var(--button_and_green_text);
	padding: 15px 15px 2px 15px;
}

.pig_vendor p {
	min-height: 16px;
}

.pig_name {
	height: 63px;
	padding: 4px 25px 2px 15px;
	color: #000;
	line-height: 19px;
	font-weight: 500;
	display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.pig_link:hover .pig_name{
	color: #016145;
}

.pig_price {
	display: flex;
	align-items: center;
	padding: 15px;
	padding-bottom: 2px;
}

.pig_price_main {
	font-weight: 300;
	color: #000;
}

.pig_price_second {
	font-size: 13px;
	font-weight: 300;
	color: #959595;
	text-decoration: line-through;
	margin-left: 11px;
}

.pig_actions {
	display: flex;
	align-items: center;
	padding: 15px;
}

.pig_atc {
	border-radius: 1px;
	background: var(--button_and_green_text);
	width: 134px;
	height: 37px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	user-select: none;
	cursor: pointer;
}

.pig_atc:hover{
	opacity: .9
}

.pig_opts {
	width: 165px;
	height: 37px;
	border: 1px solid var(--button_and_green_text);
	border-radius: 1px;
	display: flex;
	justify-content: center;
	align-items: center;
	color: var(--button_and_green_text);
	user-select: none;
	cursor: pointer;
	transition: .2s;
}

.pig_opts:hover {
	background: var(--button_and_green_text);
	color: #fff;
}

.pig_wish {
	width: 25.5px;
	height: 22.6px;
	background-image: url(/img/gold_heart.svg);
	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
	margin-left: 15px;
}

.pig_wish.wished {
	background-image: url(/img/gold_heart_fill.svg);
}

.pig_bottom {
	margin-top: auto;
	background: var(--light_shade);
	padding: 17px 21px 12px 10px;
    /* height: 61px; */
    padding-left: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.pig_bot_line {
	display: flex;
	align-items: center;
}

.pig_bot_line_top {
	margin-bottom: 5.5px;
}

.pigbl_img {
	width: 23px;
	text-align: center;
	margin-right: 10px;
}

.pic-bag{
	width: 11px;
	height: 12px;
	margin-left: 5px;
}

.pig_bot_line_bot .pigbl_img {
	width: 11px;
	margin-left: 5px;
}

.pigbl_ship_text {
	font-size: 13px;
	font-weight: 500;
	color: var(--button_and_green_text);
}

.pigbl_bag_text {
	font-size: 13px;
	font-weight: 300;
	color: #000;
}

.pro_pag_wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 25px 0;
}

.pag_left,
.pag_right {
	width: 25px;
	height: 25px;
	border-radius: 50px;
	background-position: center;
	background-size: cover;
	background-repeat: no-repeat;
	background-image: url(/img/pag_left.svg);
	margin: 0 10px;
	cursor: pointer;
	user-select: none;
}
 
.pag_right {
	background-image: url(/img/pag_right.svg);
}

.cur_page,
.pag_num {
	font-size: 19px;
	padding: 5px;
	margin: 5px;
	cursor: pointer;
	color: #000;
	user-select: none;
}

.cur_page {
	font-weight: 600;
}

.pag_num {
	font-weight: 200;
}

@media screen and (max-width: 1300px) {
	.product_item-grid {
	    width: calc(33.3333% - 5px);
	}
}

@media screen and (max-width: 1000px) {
	.products_sidebar {
		display: none;
	}
	.products_content_wrapper {
	    width: 100%;
	    padding: 0 10px;
	}
}

/*
Products List
*/

.products_grid_wrapper .pig_highlights {
	display: none;
}

.products_list_wrapper .product_item-grid {
	width: 100%;
	margin: 0;
	margin-bottom: 10px;
}

.products_list_wrapper .pig_link {
    display: flex;
}

.products_list_wrapper .pig_images_wrapper {
	width: 30%;
	height: 100%;
}

.products_list_wrapper .pig_info {
	display: flex;
	padding: 20px 20px 16px 10px;
}

.products_list_wrapper .pig_vendor,
.products_list_wrapper .pig_price,
.products_list_wrapper .pig_actions {
	padding: 0;
}

.products_list_wrapper .pig_name {
	font-size: 19px;
	padding: 0;
	margin-top: 6px;
	height: auto;
	margin-bottom: 12px;
}

.pig_link:hover .pig_img_main{
	-ms-transform: scale(1.1); /* IE 9 */
	-webkit-transform: scale(1.1); /* Safari 3-8 */
	transform: scale(1.1);transition: transform .1s;
}

.pig_highlights {
	list-style-position: outside;
	font-size: 14px;
	font-weight: 300;
	line-height: 24px;
	color: #000;
	margin-top: 20px;
}

.pig_highlights ul,
.pig_highlights ol,
.large_product_highlight ul,
.large_product_highlight ol {
	padding-left: 17px;
}

.products_list_wrapper .pig_price {
	margin-top: auto;
	margin-bottom: 15px;
}

.products_list_wrapper .pig_price .pig_price_main {
	font-size: 17px;
	font-weight: 500;
}

.products_list_wrapper .pig_top_wrap {
	width: calc(100% - 275px);
	margin-left: auto;
}

.products_list_wrapper .pig_bottom_wrap {
	height: 100%;
	display: flex;
	flex-direction: column;
	width: 215px;
	padding-left: 15px;
	margin-left: auto;
}

.products_list_wrapper .pig_bottom {
	background: #fff;
	padding: 0;
}

/*
ASSISTANT
*/

.assist_wrap {
	margin-bottom: 23px;
	display: flex;
	justify-content: space-between;
}

.assist_left {
	position: relative;
}

.assist_person {
	width: 74px;
	height: 74px;
	border-radius: 50%;
	position: relative;
}

.assist_person img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 50%;
}

.assist_online {
	width: 14px;
	height: 14px;
	background: #00BC74;
	border-radius: 50%;
	position: absolute;
	bottom: 3px;
	left: 3px;
}

.assist_btn {
    border: 0;
    background: transparent;
    width: 25px;
    height: 25px;
    position: absolute;
    right: 0;
    top: 85px;
    cursor: pointer;
}

.assist_box {
	background: #E8EDEB;
	border-radius: 0 10px 10px 10px;
	position: relative;
	width: calc(100% - 89px);
	padding: 12px 13px 0 26px;
	display: flex;
	flex-direction: column;
	height: 137px;
}

.assist_box:after {
	content: '';
	position: absolute;
	left: 0;
	top: 16px;
	width: 0;
	height: 0;
	border: 16px solid transparent;
	border-right-color: #E8EDEB;
	border-left: 0;
	border-top: 0;
	margin-top: -16px;
	margin-left: -16px;
}

.assist_box_top_top {
	display: flex;
	align-items: center;
}

.assist_box_name {
	font-size: 12px;
	color: #F9A73D;
	margin-right: auto;
}

.assist_box_number {
	font-size: 12px;
	color: #aaa;
	font-weight: 300;
}

.assist_box_number span {
	color: var(--button_and_green_text);
	font-weight: 400;
	user-select: none;
	cursor: pointer;
}

.assist_box_close {
	background: #E0CDC2;
	width: 35px;
	height: 14px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 10px;
	color: #fff;
	font-weight: 500;
	cursor: pointer;
	user-select: none;
	border-radius: 13px;
	margin-left: 12px;
}

.assist_box_close:after {
	content: 'Close';
}

.assist_box_question {
	color: var(--button_and_green_text);
	font-size: 20px;
	font-weight: 400;
	margin-top: 7px;
}

/*
*/


.tiblock {
    align-items: center;
    display: flex;
    height: 17px;
}

.ticontainer .tidot {
    background-color: #a8c3ba;
}

.tidot {
    -webkit-animation: mercuryTypingAnimation 1.5s infinite ease-in-out;
    border-radius: 10px;
    display: inline-block;
    height: 8px;
    margin-right: 6px;
    width: 8px;
}

@-webkit-keyframes mercuryTypingAnimation{
	0%{
	  	-webkit-transform:translateY(0px)
	}
	28%{
	  	-webkit-transform:translateY(-5px)
	}
	44%{
	  	-webkit-transform:translateY(0px)
	}
}

.tidot:nth-child(1){
	-webkit-animation-delay:200ms;
}
.tidot:nth-child(2){
	-webkit-animation-delay:300ms;
}
.tidot:nth-child(3){
	-webkit-animation-delay:400ms;
}

.ticontainer {
    height: 24px;
    display: flex;
    margin-top: 7px;
    align-items: flex-end;
}

/*
*/

.assist_box_bottom {
	margin-top: 15px;
}

.assist_suggestions {
	display: flex;
}

.assist_track::-webkit-scrollbar{
	width: 10px;
	height: 5px;
}

.assist_track::-webkit-scrollbar-thumb{
	background: #888;
	border-radius: 50px;
}

.assist_track::-webkit-scrollbar-track{
	box-shadow: inset 0 0 5px grey;
	border-radius: 50px;	
}

.assist_track {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
    width: calc(100% + 13px);
    margin-right: -13px;
}

.assist_track_inner {
	display: flex;
	position: relative;
}

.assist_suggest {
	color: rgba(0,0,0,.5);
	font-size: 16px;
	font-weight: 500;
	background: #fff;
	border-radius: 43px;
	padding: 13.5px 28px;
	cursor: pointer;
	user-select: none;
	margin-right: 12px;
	display: inline-flex;
	flex-direction: column;
	align-items: center;
	white-space: pre;
	margin-bottom: 18px;
	/*transition: .2s;*/
}

.assist_suggest:hover {
	background: #a8c3ba;
	color: #fff;
}

.assist_suggest_selected,
.assist_suggest_selected:hover {
	background: var(--button_and_green_text);
	color: #fff;
}

.assist_suggest_showall {
	background: #D0DBD7;
	width: 110px;
}

.assist_box_closed {
	height: 74px;
}

.assist_box_closed .assist_box_bottom {
	display: none;
}

.assist_box_closed .assist_box_close:after {
	content: 'Show';
}

.assist_suggest_image {
    color: rgba(0,0,0,.5);
    font-size: 16px;
    font-weight: 500;
    background: #fff;
    border-radius: 10px;
    padding: 9px 25px;
    cursor: pointer;
    user-select: none;
    margin-right: 12px;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    border: 2px solid #fff;
}

.assist_suggest_image.assist_suggest_selected,
.assist_suggest_image:hover {
	border: 2px solid var(--button_and_green_text);
}

.assist_suggest_image.assist_suggest_showall {
	background: #D0DBD7;
	display: flex;
	align-items: center;
	justify-content: center;
	border-color: #D0DBD7;
}

.assist_suggest_showall {
	transition: .2s;
}

.assist_suggest_showall:hover {
	background: var(--button_and_green_text);
	color: #fff;
}

.asi_image_wrap {
	width: 72px;
	height: 50px;
}

.asi_image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.assist_suggest_image .asi_text {
	font-size: 13px;
	font-weight: 500;
	margin-top: 10px;
	white-space: pre;
}

/*
Added To Cart Slide Up
*/

.atc_slideup_wrapper {
	width: 100%;
	height: 100%;
	position: fixed;
	z-index: 100;
	top: 0;
	left: 0;
	display: flex;
	overflow: auto;
	padding-top: 50vh;
}

.atc_slideup_bg {
	position: absolute;
	top: 0;
	left: 0;
	background: rgba(0,0,0,.36);
	width: 100%;
	height: 100%;
}

.atc_slideup_cont {
	background: #F9F6F4;
	border-radius: 4px 4px 0 0;
	box-shadow: 0 3px 6px rgba(0,0,0,.16);
	padding: 24px 40px 50px;
	position: relative;
	margin-top: auto;
	z-index: 1;
	width: 100%;
	/*max-height: 70%;*/
}

.atc_slideup_ex {
	width: 23px;
	height: 23px;
	position: absolute;
    cursor: pointer;
    right: 20px;
    top: -65px;
}

.atc_s_top {
	display: flex;
	align-items: center;
}

.ast_img {
	width: 60px;
	height: 60px;
}

.ast_img img {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.ast_ttl {
    color: var(--button_and_green_text);
    font-size: 30px;
    font-weight: 500;
    background-image: url(/img/atc_check.svg);
    display: inline-block;
    padding-left: 45px;
    background-repeat: no-repeat;
    background-position: left center;
    margin-left: 20px;
}

.atc_top_btns {
	margin-left: auto;
}

.atc_s_bot {
	justify-content: center;
}

.atc_s_bot .atc_top_btns,
.atc_s_bot .atc_top_btns .atc_top_btn {
	margin: auto;
}

.atc_top_btn {
    font-size: 23px;
    font-weight: 500;
    color: #fff;
    padding: 14px 37px;
    border-radius: 1px;
    cursor: pointer;
    user-select: none;
}

.atc_top_btn_cart {
	background: #F9A73D;
}

.atc_top_btn_checkout {
	background: var(--button_and_green_text);
	margin-left: 16px;
}

.atc_slideup_track_wrap {
	/*overflow: auto;*/
	/*height: 500px;*/
}

.atc_slideup_track_inner {
	display: flex;
	flex-direction: column;
}

.atc_slideup_track_inner > .boxed_width {
	width: 100%;
}

.atc_bot_btn_cart {
	color: var(--darkgreen);
	border: 1px solid var(--darkgreen);
}

/*
BAG STYLES
*/

.bag_boxed {
	display: flex;
	padding-top: 50px;
	padding-bottom: 50px;
}

.bag_left {
	width: calc(100% - 322px);
	padding-right: 23px;
}

.bag_top_member {
	background: var(--gold_shade);
	padding: 30px 20px 20px;
	margin-bottom: 15px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.btm_ttl {
	font-size: 29px;
	font-weight: 500;
	text-align: center;
	margin-bottom: 12px;
}

.btm_txt {
	font-size: 20px;
	font-weight: 300;
	margin-bottom: 15px;
}

.btm_btn {
	background: var(--gold);
	color: #fff;
	width: 172px;
	height: 56px;
	border-radius: 1px;
	font-size: 23px;
	font-weight: 500;
}

.bag_left_inner {
	background: #fff;
	padding: 20px;
	border: 1px solid var(--gold_outline);
}

.bag_ttl {
	font-size: 27px;
	font-weight: 500;
	padding-bottom: 15px;
	text-align: center;
}

.bag_item {
	border-top: 1px solid var(--gold_outline);
	padding: 35px 0;
	display: flex;
	position: relative;
}

.bag_item_left {
	width: calc(100% - 150px);
    display: flex;
    align-items: flex-start;
}

.bil_image {
	width: 75px;
	height: 75px;
	margin-right: 30px;
}

.bil_image img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.bil_text {
	width: calc(100% - 105px);
}

.bil_vendor {
	color: var(--button_and_green_text);
	font-size: 14px;
	font-weight: 300;
	line-height: 20px;
}

.bil_name {
	font-size: 16px;
	font-weight: 500;
    line-height: 23px;
    margin-bottom: 10px;
    display: block;
    color: #000;
}

.bil_opts {
	font-size: 13px;
	font-weight: 200;
}

.bil_price {
	font-size: 20px;
	font-weight: 500;
	line-height: 26px;
	margin-top: 10px;
}

.bil_gift_wrap {
	display: flex;
	align-items: center;
	margin-top: 15px;
}

.bgw_btn {
	width: 121px;
	height: 28px;
	background: var(--gold_shade);
	font-size: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	user-select: none;
	color: #000;
	padding-left: 18px;
	background-image: url(/img/gift_black.svg);
    background-size: 14px;
    background-repeat: no-repeat;
    background-position: 12px center;
}

.bgw_txt {
	font-size: 14px;
	margin-left: 15px;
	font-weight: 300;
	display: flex;
	/* align-items: flex-end; */
}

.bgw_txt span{
	font-weight: 500;
}

.bgw_rmv{
	margin-left: 5px;
	cursor: pointer;
}

.bag_item_right {
	/*height: 100%;*/
	display: flex;
	width: 150px;
	align-items: flex-end;
	flex-direction: column;
}

.bir_qty_box {
	width: 71px;
	height: 56px;
	border: 1px solid var(--gold_outline);
	border-radius: 0;
	background: #fff;
	/* padding: 0 15px; */
}

.bir_qty {
	font-size: 23px;
	font-weight: 300;
	width: 100%;
	height: 100%;
	background: #fff;
	border: 0;
    background-image: url(/img/grey_arrow_down.svg);
    background-repeat: no-repeat;
    background-position: 85%;
	padding-right: 17px;
	padding-left: 17px;
    background-size: 11px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.sc-quan select, .sc-quan input{
    font-size: 16px!important;
}

input.bir_qty {
   background-image: none;
   text-align: center;
   padding-right: 0;
}

.bir_bot_acts {
    display: flex;
    margin-top: auto;
}

.bag_item_remove,
.bag_item_save {
	font-size: 14px;
	font-weight: 200;
	cursor: pointer;
	user-select: none;
}

.bag_item_remove {
	margin-right: 7px;
	padding-right: 7px;
	border-right: 1px solid #000;
}

.bag_item_save {

}

.bag_right {
	width: 322px;
}

.bag_summary_wrapper,
.bag_promo_wrapper {
	padding: 20px;
	background: #fff;
	border: 1px solid var(--gold_outline);
	border-radius: 1px;
}

.bag_summary_ttl {
	font-size: 27px;
	font-weight: 500;
	border-bottom: 1px solid var(--gold_outline);
	padding-bottom: 20px;
	text-align: center;
}

.bag_summary_ship_wrap,
.bag_summary_info_wrap {
	display: flex;
	flex-direction: column;
	align-items: center;
	border-bottom: 1px solid var(--gold_outline);
	padding: 11px 0;
}

.bssw_ttl {
	font-size: 12px;
	font-weight: 500;
}

.bssw_line_wrapper {
	padding: 11px 0;
	display: flex;
	width: 100%;
}

.bssw_line_numbs {
	font-size: 11px;
	font-weight: 200;
}

.bssw_line {
	width: calc(100% - 65px);
	height: 8px;
	border-radius: 30px;
	background: #C6C6C6;
	position: relative;
	margin: auto;
}

.bssw_line_inner {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	border-radius: 30px;
	background: var(--button_and_green_text);
}

.bssw_pickup {
	color: var(--darkgreen);
	font-size: 12px;
	font-weight: 500;
}

.bsi_single {
    width: 100%;
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    font-weight: 300;
    line-height: 37px;
}

.bsi_zip {
	/*margin-right: auto;*/
	margin: 10px auto 10px 0;
}

.bsi_zip_txt {
	font-size: 11px;
	font-weight: 200;
	line-height: 17px;
}

.bsi_zip_txt span {
	text-decoration: underline;
	cursor: pointer;
	user-select: none;
}

.bag_ot {
	
}

.bot_txt {
	width: 100%;
	display: flex;
	justify-content: space-between;
	font-size: 19px;
	font-weight: 500;
	margin: 20px 0;
}

.bot_co_btn {
	width: 100%;
	height: 56px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: var(--button_and_green_text);
	color: #fff;
	font-size: 23px;
	font-weight: 500;
}

.bot_co_btn:hover {
	opacity: .9;
}

.bag_promo_wrapper {
	margin-top: 16px;
}

.bp_inner_wrap {
	width: 100%;
	height: 56px;
	border: 1px solid var(--gold_outline);
	border-radius: 1px;
	/* padding: 15px; */
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.bp_inner_wrap .bir_qty{
	background-position: 95%;
}

.promo_field {
	font-size: 17px;
	font-weight: 300;
	color: #707070;
	border: 0;
	padding-left: 17px;
	padding-right: 17px;
	width: 100%;
}

.promo_apply {
	font-size: 17px;
	font-weight: 500;
	color: var(--darkgreen);
	user-select: none;
	cursor: pointer;
	margin-right: 17px;
}

.tab_total {
	display: none;
}

/*
PRE CHECKOUT
*/

.ch-currency-wrapper{
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
    margin-top: -15px;
}

.pre_checkout_wrapper {
	max-width: 1020px;
	display: flex;
	align-items: flex-start;
	padding-top: 50px;
	margin-bottom: 20px;
}

.pre_checkout_left {
	width: 50%;
	/* min-width: 490px; */
	padding-right: 20px;
	border-right: 1px solid var(--gold_outline);
}

.pre_checkout_ttl {
	font-size: 26px;
	font-weight: 500;
	margin-bottom: 20px;
}

.pcl_input_set {
    width: 100%;
    height: 56px;
    background: #fff;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.pcl_input_text {
	border: 1px solid var(--gold_outline);
    padding-left: 20px;
	width: 100%;
	height: 100%;
	font-size: 17px;
	font-weight: 300;
	color: #707070;
}

.pcl_input_text:focus {
	border-color: var(--gold);
}

.pcl_submit_set {
	padding-left: 0;
}

.pcl_input_submit,
.no_account_cont {
	border: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 23px;
	font-weight: 500;
	color: #fff;
	background: var(--button_and_green_text);
	cursor: pointer;
}

.pcl_input_submit:hover,
.no_account_cont:hover {
	opacity: .95;
}

.pre_checkout_text {
	font-size: 20px;
	font-weight: 200;
}

.pre_checkout_link {
	color: var(--button_and_green_text);
}

.no_account_cont {
	height: 56px;
	margin-top: 22px;
	margin-bottom: 15px;
	border: 1px solid #959595;
}

.no_account_cont:last-child {
	margin-bottom: 0;
}

.pre_checkout_right {
	width: 50%;
	padding-left: 20px;
}

/*
CHECKOUT
*/

.checkout_wrapper {
	max-width: 940px;
	display: flex;
	align-items: flex-start;
	padding-top: 50px;
	padding-bottom: 50px;
}

.checkout_left {
	width: calc(100% - 322px);
	padding-right: 21px;
}

.checkout_card {
	width: 100%;
	background: #fff;
	padding: 20px;
	border: 1px solid var(--gold_outline);
	margin-bottom: 15px;
}

.checkout_card .bir_qty{
	background-position: 95%;
}

.checkout_card_pad_40 {
	padding: 40px;
}

.checkout_card_pad_30 {
	padding: 30px;
}

.checkout_card_pad_none {
	padding: 0;
}

.checkout_card_ttl {
	margin-bottom: 20px;
	font-size: 26px;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.checkout_card_ttl span {
	font-size: 15px;
	color: #707070;
	font-weight: 300;
}

.checkout_cb_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 19px 18px 15px 21px;
    border: 1px solid var(--gold_outline);
    cursor: pointer;
    user-select: none;
}

.checkout_cb_box:last-child {
	margin-bottom: 0;
}

.ccb_left {
	display: flex;
	align-items: center;
}

.checkout_cb {
    display: none;
}

.checkout_cb_vis {
    border: 1px solid var(--button_and_green_text);
    width: 14px;
    height: 14px;
    border-radius: 1px;
    display: block;
}

/* .checkout_cb:checked + .checkout_cb_box {
	border-color: var(--gold);
	box-shadow: 0 0 0 0.5px var(--gold);
} */

.checkout_label {
	font-size: 15px;
	font-weight: 300;
	line-height: 24px;
}

.checkout_label span {
	font-weight: 500;
}

.ccb_right {
	font-size: 15px;
	font-weight: 300;
	margin-left: 15px;
}

.checkout_half_wrap {
	display: flex;
	justify-content: space-between;
}

.checkout_half_wrap .pcl_input_set {
	width: calc(50% - 7.5px);
}

.checkout_payment_btns_wrap {
	display: flex;
	justify-content: flex-start;
	margin-bottom: 20px;
}

.checkout_payment_btn {
	padding: 11.5px 23.2px;
	font-size: 20px;
	font-weight: 500;
	margin-right: 15px;
	color: #707070;
	border-radius: 1px;
	cursor: pointer;
	user-select: none;
	color: #000;
	border: 1px solid var(--gold_outline);
}

.checkout_payment_btn.checkout_payment_btn_active {
	border: 1px solid var(--gold);
}

.checkout_payment_pass {
	border-top: 1px solid var(--gold_outline);
	padding-top: 20px;
}

.checkout_payment_pass_txt {
	font-size: 15px;
	font-weight: 500;
	/* margin-bottom: 20px; */
}

.checkout_payment_pass_txt span {
	font-weight: 300;
}

.checkout_right {
	width: 322px;
    position: sticky;
    top: 21px;
}

.checkout_terms_txt {
	font-size: 12px;
	font-weight: 200;
	text-align: center;
    padding: 0 30px;
    margin-bottom: 11px;
    line-height: 17px;
}

.checkout_terms_txt a {
	color: var(--button_and_green_text);
}

.checkout_card .bag_summary_info_wrap {
	padding-top: 0;
}

.checkout_item {
	padding: 25px 25px 15px 30px;
}

.checkout_item:first-child {
	border-top: 0;
}

.checkout_item .bag_item_left {
	width: 100%;
}

.checkout_item .bil_vendor {
	color: var(--button_and_green_text);
}

.checkout_item .bil_name {
	color: #000;
}

.checkout_item .bil_qty {
	font-weight: 300;
	color: #959595;
	font-size: 13px;
}

.checkout_item .bgw_txt {
	color: #959595;
	font-size: 13px;
}

.checkout_item .bgw_remove {
	color: #CE0D0D;
	font-weight: 400;
	margin-left: 3px;
	font-size: 13px;
	cursor: pointer;
	user-select: none;
}

.review_card_ttl {
	margin-bottom: 30px;
}

.review_card_ttl .review_change {
	color: var(--button_and_green_text);
}

.checkout_cb_static {
	padding: 0;
	border:	0;
}

.checkout_cb_static .checkout_label {
	margin-left: 0;
}

.checkout_review_txt {
	font-size: 15px;
	font-weight: 300;
	line-height: 22px;
}

.review_change {
	user-select: none;
}

.review_change span[data-clickable="true"] {
	text-decoration: underline;
	color: #000;
	cursor: pointer;
}

.review_change span[data-clickable="false"] {
	color: var(--button_and_green_text);
}

.checkout_cb_box.ccb_offers {
    border: 0;
    padding: 0;
}

.checkout_cb_box.ccb_offers .checkout_label {
	margin-left: 11px;
}

.checkout_cb:checked + .checkout_cb_vis {
    background-image: url(/img/green_check_small.png);
    background-size: 125%;
    background-position: center;
}

/*
Gift Popup
*/

.gift_overlay {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	position: fixed;
	padding-top: 17px;
	padding-bottom: 30px;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	z-index: 100;
	background: rgba(0,0,0,.57);
	overflow: auto;
}

.gift_popup_box {
	width: 640px;
	background: #fff;
}

.gpb_head {
	padding: 17px 16px 15px 36px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid var(--gold_outline);
}

.gpb_head_ttl {
	font-size: 21px;
	color: #000;
}

.gpb_head_ttl span {
	font-size: 16px;
}

.gpb_head_ex {
	width: 23px;
	height: 23px;
	cursor: pointer;
	user-select: none;
}

.gpb_body {
	padding: 18px 36px 23px;
	border-bottom: 1px solid var(--gold_outline);
}

.gpb_item {
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}

.gpb_item_img {
	width: 50px;
	height: 50px;
	margin-right: 20px;
}

.gpb_item_info {
	width: calc(100% - 70px);
	margin-bottom: 30px;
}

.gpb_item_vendor {
	font-size: 14px;
	color: var(--button_and_green_text);
	margin-bottom: 9px;
	font-weight: 200;
}

.gpb_item_name {
	font-size: 16px;
	color: #000;
	font-weight: 500;
}

.gpd_options_wrap {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	margin-bottom: 5px;
}

.gpd_option {
	width: 100%;
	margin-bottom: 15px;
	display: flex;
	flex-direction: row;
	align-items: center;
	border: 1px solid var(--gold_outline);
	padding: 13px;
	cursor: pointer;
	user-select: none;
}

.gpd_option_selected {
	border: 2px solid var(--gold);
}

.gpd_option_disc {
	font-size: 15px;
	font-weight: 200;
	text-align: center;
	margin-top: 10px;
}

.gpd_option_img {
	background: #E3E3E3;
	width: 68px;
	height: 68px;
	margin-right: 19px;
}

.gpd_option_name {
	font-size: 21px;
	font-weight: 500;
	text-align: center;
	width: calc(100% - 87px);
	display: flex;
	justify-content: space-between;
}

.gpd_option_name span {
	font-size: 21px;
	font-weight: 200;
}

.gpd_input_set {
	margin-bottom: 20px;
}

.gis_label {
	font-weight: 200;
	font-size: 18px
}

.gis_desc {
	font-size: 13px;
	font-weight: 300;
	margin-top: 7px;
}

.gis_select {
	width: 226px!important;
	margin-top: 7px;
}

.gis_select .bir_qty {
	font-weight: 400;
	font-size: 21px; 
	background-position: 90%;
}

.gis_ta {
	padding-top:12px;
	width: 100%;
	height: 133px;
	border: 1px solid var(--gold_outline);
	padding: 7px;
	font-size: 12px;
	margin-top: 7px;
	font-family: proxima-nova, sans-serif;
	resize: none;
}

.gpb_body .gpb_head_ttl {
	margin-bottom: 10px;
}

.gis_input {
	width: 100%;
	height: 56px;
	font-size: 23px;
	padding-left: 15px;
	border: 1px solid var(--gold_outline);
	margin-top: 7px;
}

.gpd_btn {
	margin-bottom: 0;
}

/*
Thank You Page
*/

.ty_boxed {
	max-width: 640px;
	margin-top: 70px;
}

.ty_boxed .checkout_card {
	/*padding-left: 100px;
	padding-right: 100px;*/
	padding: 35px 100px;
}

.co_bb {
 	border-bottom: 1px solid var(--gold_outline);
 	margin-bottom: 20px;
 	padding-bottom: 20px;
}

.text_center {
	text-align: center;
}

.ty_head {
	font-size: 35px;
	font-weight: 500;
	line-height: 32px;
	margin-bottom: 10px;
}

.ty_on {
	color: var(--button_and_green_text);
	font-size: 22px;
	font-weight: 300;
	line-height: 32px;
	margin-bottom: 10px;
}

.ty_email_not {
	font-size: 16px;
	font-weight: 300;
	line-height: 21px;
}

.ty_email_not_email {
	font-weight: 400;
}

.tyca_head {
	font-size: 19px;
	font-weight: 500;
	line-height: 32px;
	margin-bottom: 10px;
}

.tyca_items {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
    justify-content: center;
}

.tyca_item {
	width: 25%;
	padding: 10px;
}

.tyca_item img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
	object-position: center;
}

.tycad_head,
.tycad_name,
.tycad_address {
	font-size: 19px;
	font-weight: 300;
	line-height: 29px;
}

.tycad_name {
	font-weight: 500;
}

.tyctp_top,
.tyctp_bot {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.tyctp_top {
	margin-bottom: 10px;
}

.tyctp_top p {
	font-size: 19px;
	font-weight: 500;
}

.tyctp_bot p {
	line-height: 21px;
	font-size: 15px;
	font-weight: 300;
}

.tyc_cta {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.tyc_cta_btn {
	margin-bottom: 15px;
}

.tyc_cta_link {
	font-size: 19px;
	font-weight: 300;
	color: var(--button_and_green_text);
}

button[data-mb='*'] {
	margin-bottom: attr(data-mb)px;
}

.account_popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: #fff;
    z-index: 500;
    display: flex;
    justify-content: center;
    align-items: center;
}

.account_popup_ex {
	width: 23px;
	height: 23px;
	position: absolute;
	top: 20px;
	right: 20px;
	cursor: pointer;
	user-select: none;
}

.account_popup_ex img {
	width: 100%;
}

.account_popup_inner {
	max-width: 490px;
	width: 100%;
	padding-left: 10px;
	padding-right: 10px;
}

.account_popup_inner .bir_qty{
	background-position: 95%;
}

.account_popup_inner .review_change {
	font-size: 15px;
	font-weight: 500;
	color: var(--button_and_green_text);
}

footer {
	background-color: var(--light_shade);
	padding: 0 20px;
}

.footer_top {
	padding: 20px 0;
	border-bottom: 1px solid var(--gold_outline);
}

.footer_top_inner {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.footer_cols_section {
	padding: 30px 0;
	border-bottom: 1px solid var(--gold_outline);
}

.footer_cols_section_inner {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;	
}

.ftb_right {
	font-size: 20px;
	color: #000;
}

.ftb_right a {
	color: var(--button_and_green_text);
}

.ftb_right_mob,
.ftb_left.footer_social_wrap.tab_social,
.sub_ttl_mob {
	display: none;
}

.fcsa_images {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.fcs_apps a {
	font-size: 0;
	margin-bottom: 7px;
}

.fcs_col {
    display: flex;
    flex-direction: column;
}

.fcs_sitemap .fcs_txt {
	margin-top: 7px;
}

.fcs_ttl {
	font-size: 18px;
	color: var(--button_and_green_text);
	margin-bottom: 10px;
	font-weight: 500;
}

.fcs_txt {
	font-size: 15px;
	color: #000;
	line-height: 23px;
}

.fcs_txt a {
	color: var(--button_and_green_text);
}

.fcs_shipping {
	width: 175px;
	padding: 0 10px;
}

.fcs_returns {
	width: 200px;
	padding: 0 10px;
}

.footer_sub_form {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.fsf_input {
	height: 56px;
}

.fsfi_email {
    font-size: 17px;
    font-weight: 300;
    padding-left: 24px;
    margin-bottom: 10px;
}

.fsfi_btn {
    font-size: 23px;
    font-weight: 500;
    color: #fff;
    background-color: var(--button_and_green_text);
	font-family: proxima-nova, sans-serif;
	cursor: pointer;
}

.fsfi_btn:hover{
	opacity: .9;
}

.footer_bottom {
	padding: 15px 0;
}

.footer_bottom_inner {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.footer_bottom_text {
	color: var(--header_texture);
	font-size: 12px;
}

.ftb_left.footer_social_wrap {
	font-size: 0;
	display: flex;
	align-items: center;
}

.fsw_icons a {
	margin-left: 20px;
}

.footer_social_wrap p {
	font-size: 18px;
}

.fbt_links:first-child {
	margin-right: 8px;
	padding-right: 8px;
	border-right: 1px solid var(--header_texture);
}

/* Error Pages */

.error-wrap-outer{
	width: 100%;
    display: flex;
	justify-content: center;
	margin-bottom: 50px;
}

.error-wrap-inner{
	max-width: 598px;
	margin-top: 75px;
	display: flex;
    flex-direction: column;
    align-items: center;
}

.error-img-wrap{
	display: flex;
    justify-content: center;
    align-items: center;
}

.error-msg-wrap ,.error-message{
	margin-top: 25px;
	display: flex;
    flex-direction: column;
    justify-content: center;
	align-items: center;
	text-align: center;
	font-size: 19px;
	line-height: 30px;
	color: #000000;
	font-weight: 300;
}

.error-msg p:first-child{
	font-weight: 500;
}

.error-def{
	font-size: 31px;
	font-weight: 500;
}

.btn-home{
	margin-top: 25px;
	width: 170px;
	height: 56px;
	color: white;
	background-color: #016145;
	border-radius: 1px;
	cursor: pointer;
	font-size: 23px;
	display: flex; 
    justify-content: center; 
    align-items: center;
}

.btn-home:hover{
	opacity: .7;
}

.back-prev{
	color: #016145;
	font-size: 17px;
	margin-top: 25px;
	cursor: pointer;
}

.mobile-hero-slider{
	display: none;
}

.return_ttl {
	font-size: 36px;
	text-align: center;
	margin-bottom: 50px;
	font-weight: 500;
}

.return_sub_ttl {
	font-size: 26px;
	text-align: center;
	margin-bottom: 30px;
}

.return_wrapper {
	margin-bottom: 80px;
	padding-left: 20px;
	padding-right: 20px;
}

.return_2_ttl {
	text-align: left;
	margin-bottom: 25px;
}

.return_left_inner {
	padding-top: 0;
	padding-bottom: 0;
}

.return_item:first-child {
	border-top: 0;
}

.return_dropdown_wrap {
	display: flex;
	margin-top: 30px;
}

.bir_reason_box {
	width: 300px;
	margin-left: 7px;
}

.bir_reason_box .bir_qty {
	font-size: 17px;
	background-position: 95%;
}

.return_dropdown_wrap_mob {
	display: none;
}

.return_right {
	margin-top: 68px;
}

.return_bssw_ttl {
    width: 100%;
    text-align: center;
    font-size: 15px;
    font-weight: 300;
    line-height: 20px;
}

.return_bot_txt {
	margin: 15px 0 0px;
}

.return_3_ttl {
	font-size: 26px;
}

.return_instructions_ttl {
	font-size: 21px;
	font-weight: 500;
	margin-top: 30px;
}

.return_instructions {
	margin-bottom: 7px;
}

.return_instructions_ttl.mt_0 {
	margin-top: 0;
}

.return_number {
	color: var(--button_and_green_text);
	font-size: 27px;
	font-weight: 500;
	line-height: 32px;
	margin-top: 10px;
	margin-bottom: 10px;
	overflow-wrap: break-word;
    line-height: 30px;
}

.chart-wrapper .bir_qty{
	background-position: 95%;
}

.border-danger{
    border-color: #f66 !important;
}

.txt-danger{
	color: #f66 !important;
}
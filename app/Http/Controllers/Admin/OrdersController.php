<?php

namespace App\Http\Controllers\Admin;

use App\GiftCard;
use App\GiftCardSettings;
use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\Api\GiftCardController;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ShippingController;
use App\Order;

class OrdersController extends Controller
{
    public static function captureCharge($request)
    {
        $order = Order::find($request->orderId);
        if (!data_get($order, 'meta.captured')) {
            $order->capturePayment();

            $order->update([
                'payment_status' => 'paid',
                'meta->captured' => true,
                'meta->captured_source' => 'nova at ' . now()->toDateTimeString()
            ]);
        }
    }

    public static function update($id)
    {
        $order = Order::find($id);

        // Basic existence check
        abort_if(!$order, 404, 'Order not found');

        // Validate incoming data (server-side validation)
        $rules = [];
        if (request()->has('shipping')) {
            $rules = array_merge($rules, [
                'shipping.name' => 'nullable|string|max:255',
                'shipping.phone' => 'nullable|string|max:25',
                'shipping.address_line_1' => 'required|string|max:255',
                'shipping.address_line_2' => 'nullable|string|max:255',
                'shipping.city' => 'required|string|max:255',
                'shipping.state' => 'required|string|max:255',
                'shipping.postal_code' => 'required|string|max:20',
                'shipping.country' => 'required|string|max:255',
            ]);
        }
        if (request()->has('pickup')) {
            $rules = array_merge($rules, [
                'pickup.name' => 'nullable|string|max:255',
                'pickup.email' => 'nullable|email',
                'pickup.phone' => 'nullable|string|max:25',
            ]);
        }
        if (request()->has('payments')) {
            $rules = array_merge($rules, [
                'payments.name' => 'nullable|string|max:255',
                'payments.address_line_1' => 'required|string|max:255',
                'payments.address_line_2' => 'nullable|string|max:255',
                'payments.city' => 'required|string|max:255',
                'payments.state' => 'required|string|max:255',
                'payments.postal_code' => 'required|string|max:20',
                'payments.country' => 'required|string|max:255',
            ]);
        }
        if (!empty($rules)) {
            request()->validate($rules);
        }

        $array = [];
        if (request()->shipping) {
            collect(request()->shipping)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["shipping->shippingInfo->$key" => $value], $array);
            });
        }
        if (request()->pickup) {
            collect(request()->pickup)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["shipping->pickupInfo->$key" => $value], $array);
            });
        }
        if (request()->payments) {
            collect(request()->payments)->map(function ($value, $key) use (&$array) {
                $array = array_merge(["payments->creditInfo->$key" => $value], $array);
            });
        }

        // Capture old values for audit
        $oldShipping = data_get($order, 'shipping.shippingInfo') ?: [];
        $oldPickup = data_get($order, 'shipping.pickupInfo') ?: [];
        $oldBilling = data_get($order, 'payments.creditInfo') ?: [];

        $order->update($array);

        // Build audit trail entry if there are changes
        $changes = [];
        if (request()->shipping) {
            $changes[] = [
                'type' => 'shipping',
                'before' => $oldShipping,
                'after' => request()->shipping,
            ];
        }
        if (request()->pickup) {
            $changes[] = [
                'type' => 'pickup',
                'before' => $oldPickup,
                'after' => request()->pickup,
            ];
        }
        if (request()->payments) {
            $changes[] = [
                'type' => 'billing',
                'before' => $oldBilling,
                'after' => request()->payments,
            ];
        }

        if (!empty($changes)) {
            $meta = $order->meta ?? [];
            $log = $meta['address_changes'] ?? [];
            $log[] = [
                'at' => now()->toDateTimeString(),
                'by_admin_id' => optional(auth('admin')->user())->id,
                'by_admin_email' => optional(auth('admin')->user())->email,
                'changes' => $changes,
            ];
            $meta['address_changes'] = $log;
            $order->update(['meta' => $meta]);
        }

        return response()->json(['success' => true]);
    }

    public static function refundAmount($request)
    {
        $order = Order::find($request->orderId);
        $refundAmount = (double)$request->amount;
        $message = $request->message;
        $type = $request->type;

        if ($type == 'giftCard') {
            $giftCard = GiftCard::create([

                'media' => GiftCardSettings::first()->media_urls[0],
                'amount' => $refundAmount,
                'balance' => $refundAmount,
                'to_name' => $order->customer->name,
                'from_name' => 'Eichlers Team',
                'message' => collect($message)->implode('<br/>'),
                'to_email' => $order->customer->email,
                'code' => GiftCard::createUniqueUuid(),
            ]);

            $transaction = $giftCard->transactions()->create([
                'order_id' => $order->id,
                'type' => 'Return Create'
            ]);

            GiftCardController::createVoucher($transaction);

            EmailsController::SendGiftCard($giftCard);

            $payments = [
                'giftCards' => [
                    'id' => $giftCard->id,
                    'code' => substr($giftCard->code, -4),
                ],
                'amount' => $refundAmount,
                'message' => collect($message)->implode('<br/>')
            ];

            $refunds = data_get($order, 'payments.egift_refunds') ?? [];

            $refunds = collect($refunds)->push([
                'Date' => now(),
                'Payments' => $payments
            ]);

            $order->update([
                'payments->egift_refunds' => $refunds,
                'payments->egift_refunds_total' => collect($refunds)->map->Payments->pluck('amount')->sum()
            ]);

            return;
        }
        if (+number_format($order->refundMax, 2, '.', '') < +number_format($refundAmount, 2, '.', '')) {
            $refundMax = $order->refundMax;
            abort(400, "Max refund is ${refundMax}");
        }


        $charge_left = $order->chargeLeft;

        $creditInfo = [];
        $giftCards = [];

        if ($charge_left) {
            $order->refundPayment(min($charge_left, $refundAmount));

            $creditInfo = [
                'type' => data_get($order, 'payments.creditInfo.type'),
                'amount' => $refundAmount,
                'last_four' => data_get($order, 'payments.creditInfo.last_four'),
                'payment_type' => data_get($order, 'payments.creditInfo.payment_type'),
            ];
        }

        if ($refundAmount > $charge_left) {
            $total_left = $refundAmount - $charge_left;

            $giftCards = collect(data_get($order, 'payments.giftCard'))->map(
                function ($card) use (&$total_left, $order) {
                    // $amount = $card['amount'] - min($total_left, $order->TotalRefundedForGiftCard($card['id']));
                    $amount = min($card['amount'] - $order->TotalRefundedForGiftCard($card['id']), $total_left);

                    if ($total_left > 0 && $amount > 0) {
                        $total_left -= $amount;

                        $giftCard = GiftCard::find(data_get($card, 'id'));

                        $giftCard->update([
                            'balance' => $giftCard->balance + $amount
                        ]);

                        $transaction = $giftCard->transactions()->create([
                            'order_id' => $order->id,
                            'amount' => $amount,
                            'type' => 'Refund'
                        ]);

                        GiftCardController::refillVoucher($transaction);
                        return [
                            'id' => $card['id'],
                            'code' => $card['code'],
                            'amount' => $amount
                        ];
                    }
                }
            );
        }

        $payments = [
            'creditInfo' => $creditInfo,
            'giftCards' => $giftCards,
            'amount' => $refundAmount,
            'message' => collect($message)->implode('<br/>')
        ];

        $refunds = data_get($order, 'payments.refunds') ?? [];

        $refunds = collect($refunds)->push([
            'Date' => now(),
            'Payments' => $payments
        ]);

        $order->update([
            'payments->refunds' => $refunds,
            'payments->refunds_total' => collect($refunds)->map->Payments->pluck('amount')->sum()
        ]);

        EmailsController::SendRefundOrder($payments, $order->customer);
    }


    public static function markAsShipped($request)
    {
        $request = [
            'SS-UserName' => env('SHIP_STATION_USERNAME'),
            'SS-Password' => env('SHIP_STATION_PASSWORD'),
            'order_number' => $request->orderId,
            'carrier' => 'Other',
            'service' => '',
            'tracking_number' => '',
            'source' => 'nova'
        ];
        (new ShippingController)->post(request()->merge($request));
    }

    public static function markAsDelivered($request)
    {
        $order = Order::find($request->orderId);

        if (!data_get($order, 'meta.captured')) {
            $order->capturePayment();

            $order->update([
                'payment_status' => 'paid',
                'meta->captured' => true,
                'meta->captured_source' => 'nova at ' . now()->toDateTimeString()
            ]);
        }

        $order->markAsDelivered();
    }

    public static function addToAccount($id)
    {
        $order = Order::find($id);

        $order->update([
            'guest' => false,
        ]);

        return response()->json(['success' => true]);
    }
}

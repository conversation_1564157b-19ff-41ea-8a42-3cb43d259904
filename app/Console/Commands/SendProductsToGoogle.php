<?php

namespace App\Console\Commands;

use App\Product;
use App\VariationInfo;
use Illuminate\Console\Command;
// use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;

class SendProductsToGoogle extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'send-products-to-google';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send products to google';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Storage::disk('logs')->delete('merchant.csv');

        $headers = collect([
            'id','item_group_id', 'title','description','link','price','sale_price','availability','image_link','gtin','mpn','brand','color','size', 'expiration_date'
        ]);

        \Log::channel('merchant')->info('"' . $headers->join('","') .'"');

        Product::where(function ($query) {
                return $query
                    ->Issearchable()
                    ->orWhere('updated_at','>', today()->subDays(30));
            })
            ->whereDoesntHave('variationInfos')
            ->whereIn('exclude_free_shipping', [null, 0,''])
            ->pluck('id')
            ->chunk(50)
            ->each(function ($ids) {
                dispatch(function() use ($ids){
                    $ids->each(function ($id) {
                        $product = \App\Product::find($id);
                        $data = collect([
                            $product->id,

                            // item_group_id
                            '',

                            str_replace('"', '""', $product->title),
                            str_replace('"', '""', strip_tags(htmlspecialchars_decode($product->description, ENT_QUOTES))),
                            env('APP_URL') . data_get($product, 'search.path'),
                            $product->price,

                            //sale price
                            '',

                            $product->searchable ? 'in_stock' : 'out_of_stock',
                            \Illuminate\Support\Str::replaceLast('-grid.jpg', '-large.jpg', data_get($product, 'search.image')),
                            data_get($product, 'meta.isbn 13') ?? data_get($product, 'meta.ISBN 13')
                                ?? data_get($product, 'meta.isbn 10') ?? data_get($product, 'meta.ISBN 10')
                                ?? data_get($product, 'barcode'),
                            str_replace('"', '""', $product->sku),
                            str_replace('"', '""',data_get($product, 'vendor.name')),

                            // color
                            '',

                            // size
                            '',

                            // expiration_date
                            $product->active ? '': today()->subday()->format('Y-m-d'),
                        ]);
                
                        \Log::channel('merchant')->info('"' . $data->join('","') .'"');
                    });
                })->onQueue('merchant');
            });
            // return;
        VariationInfo::active()
            ->pluck('id')
            ->chunk(50)
            ->each(function ($ids) {
                dispatch(function() use ($ids){
                    $ids->each(function ($id) {
                        $variation = \App\VariationInfo::find($id);
                        if($variation->exclude_free_shipping) {
                            return;
                        }
                        $product_json = data_get($variation, 'product_json');
                        $search = json_decode(data_get($product_json, 'search'));
                        $meta = json_decode($variation->meta);
                        $data = collect([
                            "$variation->product_id-$variation->id",
                            "$variation->product_id",
                            str_replace('"', '""', data_get($product_json, 'title')),
                            str_replace('"', '""', strip_tags(htmlspecialchars_decode(data_get($product_json, 'description'), ENT_QUOTES))),
                            env('APP_URL') . data_get($product_json, 'path')  . '?'. \Illuminate\Support\Arr::query((array)json_decode($variation->meta)),
                            $variation->price,
                            
                            //sale price
                            '',

                            $variation->max > 0 ? 'in_stock' : 'out_of_stock',
                            \Illuminate\Support\Str::replaceLast('-grid.jpg', '-large.jpg', data_get($search, 'image')),
                            data_get($meta, 'isbn 13') ?? data_get($meta, 'ISBN 13') ?? data_get($product_json, 'meta.isbn 13') ?? data_get($product_json, 'meta.ISBN 13')
                                ?? data_get($meta, 'isbn 10') ?? data_get($meta, 'ISBN 10') ?? data_get($product_json, 'meta.isbn 10') ?? data_get($product_json, 'meta.ISBN 10')
                                ?? data_get($variation, 'barcode'),
                            '',
                            str_replace('"', '""', data_get($product_json, 'vendor_name')),

                            //color
                            str_replace('"', '""', data_get($meta, 'color') ?? data_get($meta, 'Color')),

                            //size
                            str_replace('"', '""', data_get($meta, 'size') ?? data_get($meta, 'Size')),

                            // expiration_date
                            $variation->product->active ? '': today()->subday()->format('Y-m-d'),
                        ]);
                
                        \Log::channel('merchant')->info('"' . $data->join('","') .'"');
                    });
                })->onQueue('merchant');
            });
        // Artisan::call('queue:work --queue=merchant --stop-when-empty');
        return;

    }
}

<?php

namespace App\Nova;

use App\Nova\Layouts\BackgroundImageWYSIWYGLayout;
use App\Nova\Layouts\BrandsLayout;
use App\Nova\Layouts\DepartmentHeaderLayout;
use App\Nova\Layouts\DepartmentTrackLayout;
use App\Nova\Layouts\DividerLayout;
use App\Nova\Layouts\HeroImageSliderLayout;
use App\Nova\Layouts\ImageGridLayout;
use App\Nova\Layouts\MenuTrackLayout;
use App\Nova\Layouts\TextImageLayout;
use App\Nova\Layouts\TrackLayout;
use App\Nova\Layouts\TrackSideLayout;
use App\Nova\Layouts\TrendingTrackLayout;
use App\Nova\Layouts\WYSIWYGLayout;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Hidden;
use Laravel\Nova\Fields\ID;
// use Laravel\Nova\Fields\Slug;
use Laravel\Nova\Fields\Text;
use Capitalc\Checkbox\Checkbox;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Whitecube\NovaFlexibleContent\Flexible;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;

class Page extends Resource
{
    use SuperAdmin;

    public static string $model = \App\Page::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),


            Text::make('Name')
                ->rules('required'),

            Images::make('Media')
                ->croppingConfigs(['ratio' => 4 / 3])
                ->conversionOnIndexView('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnDetailView('thumb'),

            Heading::make('Page Components')->hideFromIndex(),
            Flexible::make('Components')
                ->addLayout(HeroImageSliderLayout::class)
                ->addLayout(TrackLayout::class)
                ->addLayout(TrackSideLayout::class)
                ->addLayout(DividerLayout::class)
                ->addLayout(MenuTrackLayout::class)
                ->addLayout(DepartmentTrackLayout::class)
                ->addLayout(DepartmentHeaderLayout::class)
                ->addLayout(WYSIWYGLayout::class)
                ->addLayout(TextImageLayout::class)
                ->addLayout(ImageGridLayout::class)
                ->addLayout(BackgroundImageWYSIWYGLayout::class)
                ->addLayout(BrandsLayout::class)
                ->button('Add Component')
                ->hideFromIndex(),

            Heading::make('SEO')->hideFromIndex(),
            Text::make('Title')->rules('required'),
            // Slug::make('Slug')->from('Title')->rules('required'),
            Textarea::make('Description'),
            CheckBox::make('Search Query Redirect', 'redirect')
                ->help("When set as active, search queries that match " . $this->help_name() . " will be redirected to the " . $this->help_name() . " page"),
            Hidden::make('Paths')
                ->default(\App\Http\Controllers\Admin\PathsController::index())
                ->fillUsing(function ($request, $model) {
                    return null;
                })
                ->onlyOnForms(),
            Hidden::make('Data')
                ->default(\App\Http\Controllers\Admin\PathsController::relationships())
                ->fillUsing(function ($request, $model) {
                })
                ->onlyOnForms()
        ];
    }

    public function help_name(): string
    {
        return $this->name ? "'" . $this->name . "'" : 'pages name';
    }
}

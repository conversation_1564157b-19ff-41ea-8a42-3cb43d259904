<?php

namespace App;

use App\Http\Controllers\GoogleShoppingController;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use App\Http\Controllers\PosReportsController;
use App\Traits\OnlinePriceTrait;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Traits\SaleTrait;

class VariationInfo extends Model implements HasMedia
{
    use InteractsWithMedia, SaleTrait, OnlinePriceTrait;

    protected $fillable = [
        'product_id',
        'label_id',
        'list_price',
        'store_price',
        'online_price',
        'sale_price',
        'cost_price',
        'barcode',
        'gtin',
        'sku',
        'width',
        'height',
        'length',
        'weight',
        'boxes',
        'origin',
        'system_code',
        'store_quantity',
        'max_quantity',
        'website_quantity',
        'track_inventory',
        'pos_meta',
        'store_title',
        'visibility',
        'item_type',
        'meta',
        'image',
        'product_json',
        'store_vendor',
        'store_category',
        'store_sub_category'
    ];

    public $hidden = ['product_json', 'pos_meta'];

    protected $appends = [
        'media_urls',
        'full_urls',
        'price',
        'fake_price',
        'weight',
        'sku',
        'vendor_sku',
        'max',
        'out_of_stock'
    ];

    protected $with = [
        'sale',
    ];

    protected $casts = [
        // 'pos_meta' => 'array',
        'product_json' => 'array',
    ];

    public $guarded = [];

    public function toArray()
    {
        return array_merge(
            $this->attributes,
            [
                'price' => $this->price,
                'fake_price' => $this->fake_price,
                'add_sale' => $this->add_sale,
                'sale_type' => $this->sale_type,
                'sale_from' => $this->sale_from,
                'sale_amount' => $this->sale_amount,
                'start_sale' => now()->parse($this->start_sale)->toDateString(),
                'end_sale' => now()->parse($this->end_sale)->toDateString(),
                'track_link' => $this->track_link,
                'add_online_price' => $this->add_online_price,
                'online_price_percent' => $this->online_price_percent,
                'online_price_based_on' => $this->online_price_based_on,
            ]
        );
    }


    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function label()
    {
        return $this->belongsTo(Label::class);
    }

    public function vendor() {
        return $this->hasOneThrough(
            Vendor::class,
            Product::class,
            'id',
            'id',
            'product_id',
        );
    }

    public function getPersonalizesAttribute()
    {
        return $this->product->personalizes;
    }

    public function getPrice()
    {
        return $this->price; // ?? $this->product->price;
    }

    public function getSkuAttribute()
    {
        return $this->attributes['sku'] ? $this->attributes['sku'] : data_get($this, 'product_json.sku');
    }

    public function getVendorSkuAttribute()
    {
        return $this->attributes['vendor_sku'] ? $this->attributes['vendor_sku'] : data_get($this, 'product_json.vendor_sku');
    }

    public function getPriceAttribute()
    {
        return $this->sale_price
            ?: $this->online_final_price
                ?: $this->store_price
                    ?: $this->list_price
                        ?: data_get($this->product_json, 'price');
    }

    public function getFakePriceAttribute()
    {
        $price = max(
            $this->list_price ?: data_get($this->product_json, 'list_price'),
            $this->store_price ?: data_get($this->product_json, 'store_price'),
            $this->online_final_price ?: data_get($this->product_json, 'online_final_price') ?: data_get( $this->product_json, 'online_price'),
            $this->sale_price ?: data_get($this->product_json, 'sale_price')
        );

        return $this->price != $price ? $price : null;
    }

    public function getMaxAttribute()
    {
        $max = 0;
        if (!($this->track_inventory ?? $this->product->track_inventory) || $this->item_type != 'physical') {
            $max = 999;
        } else {
            $product = $this->product;
            $store_count = $this->store_quantity ?? $product->store_quantity;
            $website_count = $this->website_quantity ?? $product->website_quantity;
            $max = max($store_count + $website_count, 0);
        }

        $max_quantity = $this->max_quantity;
        if ($max_quantity !== null) {
            return min($max_quantity, $max);
        }

        if ($max_quantity <= 0) {
            $product = $this->product;
            if($product->allow_sell_out_of_stock && data_get($product->vendor,'enable_sell_out_of_stock')){
                return 999;
            }
        }

        return $max;
    }

    public function getWeightAttribute()
    {
        return $this->attributes['weight'] ?? data_get($this->product_json, 'weight');
    }

    public function getWidthAttribute()
    {
        return $this->attributes['width'] ?? data_get($this->product_json, 'width');
    }

    public function getHeightAttribute()
    {
        return $this->attributes['height'] ?? data_get($this->product_json, 'height');
    }

    public function getLengthAttribute()
    {
        return $this->attributes['length'] ?? data_get($this->product_json, 'length');
    }

    public function scopeActive($query)
    {
        return $query->where('visibility', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('visibility', false);
    }

    public function getActiveAttribute()
    {
        return $this->visibility;
    }

    public function deductQuantity($quantity)
    {
        $deducts = $this->getDeductQuantity($quantity);
        $deduct_from_website_quantity = $deducts['website_deduct'];
        $deduct_from_store_quantity = $deducts['store_deduct'];

        if ($this->attributes['sku']) {
            $this->update([
                'website_quantity' => $this->website_quantity - $deduct_from_website_quantity,
                'store_quantity' => $this->store_quantity - $deduct_from_store_quantity
            ]);

            if ($quantity > $deduct_from_website_quantity + $deduct_from_store_quantity) {
                $this->product->deductQuantity(
                    $quantity - ($deduct_from_website_quantity + $deduct_from_store_quantity)
                );
            }
        } else {
            $this->product->deductQuantity($quantity);
        }
    }

    public function getDeductQuantity($quantity)
    {
        $has_sku = $this->attributes['sku'];
        $website_quantity = $has_sku ? $this->website_quantity : $this->product->website_quantity;
        $store_quantity = $has_sku ? $this->store_quantity : $this->product->store_quantity;

        $deduct_from_website_quantity = min($quantity, $website_quantity);
        $deduct_from_store_quantity = $quantity - $deduct_from_website_quantity; // min(max($quantity - $website_quantity, 0), $store_quantity);

        return [
            'website_deduct' => max($deduct_from_website_quantity, 0),
            'store_deduct' => $deduct_from_store_quantity
        ];
    }

    public function removeFromBags()
    {
        $bags = Bag::where([
            'model_type' => get_class($this),
            'model_id' => $this->id
        ])->orderByDesc('updated_at');

        $remove = $bags->sum('quantity') - $this->max;

        $bags->each(function ($bag) use (&$remove) {
            if ($remove >= 1) {
                $deduct = min($bag->quantity, $remove);

                $bag->update([
                    'quantity' => $bag->quantity - $deduct,
                    'active' => $bag->quantity - $deduct > 0
                ]);

                $remove = $remove - $deduct;
            }
        });
    }

    public function getFullUrlsAttribute()
    {
        return [
            'default' => $this->getFirstMediaUrl('images'),
        ];
    }

    public function getMediaUrlsAttribute()
    {
        // $images = $this->media->map(function ($item) {
        //     return $item->getUrl();
        // });
        $images = $this->getMedia('media')->sortBy('order_column')->map(function ($item) {
            if ($item->collection_name == 'media') {
                return [
                    'grid' => $item->getUrl('grid'),
                    'large' => $item->getUrl('large'),
                    'lightbox' => $item->getUrl('lightbox'),
                    'thumbnail' => $item->getUrl('thumbnail'),
                ];
            }
        })->filter()->values();

        if ($images->isEmpty()) {
            $images = data_get($this->product_json, 'media_urls');
        }

        return $images;
    }

    public function getShippableAttribute()
    {
        if($this->item_type) {
            return data_get($this, 'item_type') == 'physical' || data_get($this, 'item_type') == 'both';
        }
        return data_get($this->product_json, 'item_type') == 'physical' || data_get($this->product_json, 'item_type') == 'both';
    }

    public function getExcludeFreeShippingAttribute()
    {
        return data_get($this->product_json, 'exclude_free_shipping');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->background('#ffffff')
            ->width(130)
            ->height(130);
        $this->addMediaConversion('thumbnail')
            ->width(100);
        $this->addMediaConversion('grid')
            ->width(250);
        $this->addMediaConversion('large')
            ->width(500);
        $this->addMediaConversion('lightbox')
            ->width(1500);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('media');
    }

    public function getBillableWeightAttribute()
    {
        $dimensional_weight = ($this->width
                * $this->height
                * $this->length)
            / (settings()->getValue('dimensional_factor') ?? 139);
        if ($this->boxes > 1) {
            $dimensional_weight = $dimensional_weight * $this->boxes;
        }

        $weight = $this->boxes > 1
            ? ($this->weight ? $this->weight : 0) * $this->boxes
            : $this->weight;

        return max([
            $dimensional_weight,
            $weight
        ]);
    }

    public function getFrontEndAttribute($quantity = 1)
    {
        $media = $this->product->getMedia('variations')->firstWhere('custom_properties._id', $this->image)
            ?? optional($this->product->getMedia('media')->sortBy('order_column')->first());
        return [
            'id' => $this->id,
            'sku' => $this->sku,
            'max' => $this->max,
            'out_of_stock' => $this->out_of_stock,
            'type' => 'variation',
            'meta' => $this->meta,
            'quantity' => $quantity,
            'weight' => $this->weight,
            'price' => $this->getPrice(),
            'item_type' => $this->item_type,
            'fake_price' => $this->fake_price,
            'max_quantity' => $this->max_quantity,
            'media' => $media->getUrl('thumbnail'),
            'total' => $this->getPrice() * $quantity,
            'path' => data_get($this->product_json, 'path'),
            'title' => data_get($this->product_json, 'title'),
            'large_media' => optional($media)->getUrl('large'),
            'extended_duration' => $this->getExtendedDuration(),
            'product_id' => data_get($this->product_json, 'id'),
            'vendor' => data_get($this->product_json, 'vendor_name'),
            'exclude_free_shipping' => $this->exclude_free_shipping,
            'tax_code' => data_get($this->product_json, 'tax_code'),
            'exclude_from_returns' => $this->product->exclude_from_returns,
            'categories_string' => collect(
                data_get(json_decode(data_get($this, 'product_json.search')), 'categories')
            )->implode(', '),
            'label' => $this->getLabel(),
        ];
    }

    public function getLabel()
    {
        // If this variation has a label assigned, use it
        if ($label = $this->label) {
            return [
                'name' => $label->name,
                'color' => $label->color,
            ];
        }

        // Otherwise, fall back to the product's label logic
        return $this->product->getLabel();
    }

    public function getTaxCodeAttribute()
    {
        return data_get($this->product_json, 'tax_code');
    }

    public function getDurationAttribute()
    {
        return data_get($this->product_json, 'duration');
    }

    public function getReleaseDateAttribute()
    {
        return data_get($this->product_json, 'release_date');
    }

    public function getExtendedDuration()
    {
        $extendedDurtion = 0;
        $product = $this;
        $extendedDurtion += $product->duration ? ($product->duration + 1) : 0;
        if ($product->release_date) {
            $extendedDurtion += now()->isBefore($product->release_date) ? (now()->diffInDays(
                    $product->release_date
                ) + 1) : 0;
        }

        // vendor has delayed
        if($this->outOfStock && $this->max > 0) {
            $extendedDurtion += data_get($this->product->vendor, 'out_of_stock_delivery_days');
        }

        return $extendedDurtion;
    }

    public function getTrackLinkattribute()
    {
        $filter = base64_encode(json_encode([
            [
                'class' => 'App\Nova\Filters\InventorySearch',
                'value' => $this->sku,
            ]
        ]));
        return '/admin/resources/inventory-tracks?inventory-tracks_page=1&inventory-tracks_filter=' . $filter;
    }

    public function setTrackLinkattribute()
    {
    }

    public function updateProductJson()
    {
        $variationInfo = $this;
        $variationInfo->withoutEvents(function () use ($variationInfo) {
            $product = $variationInfo->product;
            $variationInfo->update([
                'product_json' => $product->setAppends([])->getAttributes() + [
                        'path' => $product->path,
                        'price' => $product->price,
                        'media_urls' => $product->media_urls,
                        'vendor_name' => optional($product->vendor)->name,
                        'online_final_price' => $product->online_final_price,
                    ]
            ]);
        });
    }


    public function linkedSubscriptionGroups()
    {
        return SubscriptionGroupItem::where([['model_type', 'App\VariationInfo'], ['model_id', $this->id]])
            ->pluck('subscription_group_id')->unique();
    }


    protected static function boot()
    {
        parent::boot();

        static::saving(function ($product) {
            if (!$product->attributes['sku']) {
                $product->store_price = null;
                $product->store_quantity = null;
                return;
            }
            if ($product->isDirty('sku') || $product->isDirty('visibility')) {
                $product->withoutEvents(function () use ($product) {
                    PosReportsController::GetProductInfo($product);
                });
            }
        });
        static::saved(function ($product) {
            if ($product->active) {
                dispatch(function () use ($product) {
                    GoogleShoppingController::addProduct($product);
                });
            }

            // update parent data
            Product::withoutEvents(function () use ($product) {
                $product = $product->product;
                $product->update([
                    'var_skus->skus' => $product->variationInfos->map->sku->filter()->values(),
                    'var_skus->quantity' => $product->variationInfos->map->max->filter()->sum()
                ]);
            });

        });
        static::created(function ($product) {
            if (!$product->attributes['sku']) {
                $product->store_price = null;
                $product->store_quantity = null;
                return;
            }
            $product->withoutEvents(function () use ($product) {
                PosReportsController::GetProductInfo($product);
            });
        });
    }

    public function getOutOfStockAttribute()
    {
        $max = 0;
        if (!($this->track_inventory ?? $this->product->track_inventory) || $this->item_type != 'physical') {
            $max = 999;
        } else {
            $product = $this->product;
            $store_count = $this->store_quantity ?? $product->store_quantity;
            $website_count = $this->website_quantity ?? $product->website_quantity;
            $max = max($store_count + $website_count, 0);
        }

        $max_quantity = $this->max_quantity;
        if ($max_quantity !== null) {
            return min($max_quantity, $max);
        }

        return $max <= 0;
    }
}

{"private": true, "scripts": {"dev": "vite", "build": "vite build", "format": "prettier --write ./resources/js/**/*.{js,vue} --config ./prettier.config.cjs"}, "type": "module", "devDependencies": {"axios": "^1.3.4", "bootstrap": "^5.3.0", "jquery": "^3.6.0", "lodash": "^4.17.21", "popper.js": "^1.16.1", "prettier": "2.8.8", "sass": "^1.59.0", "sass-loader": "^12.5.0", "vite-plugin-vue2": "^2.0.3", "vue": "^2.6.14"}, "dependencies": {"@bugsnag/js": "^7.1.1", "@bugsnag/plugin-vue": "^7.1.1", "@johmun/vue-tags-input": "^2.1.0", "@riophae/vue-treeselect": "0.0.38", "accounting": "^0.4.1", "algoliasearch": "^5.19.0", "animate.css": "^3.7.2", "browser-unhandled-rejection": "^1.0.2", "css-vars-ponyfill": "^2.2.1", "flatpickr": "^4.6.3", "font-awesome": "^4.7.0", "js-cookie": "^3.0.1", "laravel-nova": "^1.2.0", "laravel-vite-plugin": "^0.7.8", "laravel-vue-pagination": "^2.3.1", "moment": "^2.29.1", "payform": "^1.4.0", "popmotion": "^8.7.6", "process": "^0.11.10", "scriptjs": "^2.5.9", "simple-keyboard": "^2.28.15", "vite": "^4.5.5", "vue-clickaway": "^2.2.2", "vue-flatpickr-component": "^8.1.4", "vue-froala-wysiwyg": "^3.1.0", "vue-meta": "^2.4.0", "vue-router": "^3.5.1", "vue-select": "^3.2.0", "vuedraggable": "^2.23.2", "vuejs-clipper": "^0.2.13", "vuelidate": "^0.7.7", "vuex": "^3.6.2", "vuex-persist": "^2.1.1", "vuex-shared-mutations": "^1.0.2"}}